{"name": "digit-data-report-front", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite --mode development", "build:ts": "vue-tsc --noEmit && vite build", "build:dev": "vite build --mode development", "build:test": "vite build --mode test", "build:prod": "vite build --mode production", "build": "npm run build:prod", "lint": "eslint --cache \"src/**/*.{vue,ts}\" --fix", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.8.2", "crypto-js": "^4.2.0", "echarts": "5.6.0", "element-plus": "^2.10.4", "lodash-es": "^4.17.21", "pinia": "3.0.3", "pinia-plugin-persistedstate": "4.4.0", "reveal.js": "^5.2.1", "vue": "^3.5.13", "vue-router": "^4.5.0"}, "devDependencies": {"@eslint/js": "^9.22.0", "@types/crypto-js": "^4.2.2", "@types/lodash-es": "^4.17.12", "@types/node": "^22.13.10", "@types/reveal.js": "^5.2.0", "@typescript-eslint/eslint-plugin": "^8.26.1", "@typescript-eslint/parser": "^8.26.1", "@vitejs/plugin-legacy": "^6.0.2", "@vitejs/plugin-vue": "^5.2.1", "@vue/tsconfig": "^0.7.0", "eslint": "^9.22.0", "eslint-config-prettier": "^10.1.1", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-vue": "^10.0.0", "globals": "^15.15.0", "prettier": "^3.5.3", "sass": "^1.85.1", "terser": "^5.39.0", "typescript": "~5.7.2", "unocss": "65.4.3", "unplugin-auto-import": "^19.1.1", "unplugin-vue-components": "^28.4.1", "vite": "^6.2.0", "vue-eslint-parser": "^10.1.1", "vue-tsc": "^2.2.4"}}