import { defineConfig, presetUno, presetAttributify } from "unocss";

export default defineConfig({
  content: {
    pipeline: {
      include: [/(src).*.(s?css|vue|[jt]sx?)$/],
      exclude: [
        "node_modules",
        "dist",
        ".git",
        ".husky",
        ".vscode",
        "public",
        "build",
        "mock",
        "./stats.html"
      ]
    }
  },
  presets: [presetUno(), presetAttributify()],
  rules: [
    [
      /^c-([A-Fa-f0-9]{1,})$/,
      ([, d]) => {
        return { color: d.length === 1 ? `#${d}${d}${d}` : `#${d}` };
      }
    ],
    [
      /^bg-([A-Fa-f0-9]{1,})$/,
      ([, d]) => {
        return { "background-color": d.length === 1 ? `#${d}${d}${d}` : `#${d}` };
      }
    ],
    [/^top-(\d+)$/, ([, d]) => ({ top: `${d}px` })],
    [/^fw-(\d+)$/, ([, d]) => ({ "font-weight": `${d}` })],
    [/^w-(\d+)$/, ([, d]) => ({ width: `${d}px` })],
    [/^h-(\d+)$/, ([, d]) => ({ height: `${d}px` })],
    [/^vw-(\d+)$/, ([, d]) => ({ width: `${d}vw` })],
    [/^vh-(\d+)$/, ([, d]) => ({ height: `${d}vh` })],
    [/^fs-(\d+)$/, ([, d]) => ({ "font-size": `${d}px` })],
    [/^lh-(\d+)$/, ([, d]) => ({ "line-height": `${d}px` })],
    [/^m-(\d+)$/, ([, d]) => ({ margin: `${d}px` })],
    [/^mt-(\d+)$/, ([, d]) => ({ "margin-top": `${d}px` })],
    [/^mr-(\d+)$/, ([, d]) => ({ "margin-right": `${d}px` })],
    [/^mb-(\d+)$/, ([, d]) => ({ "margin-bottom": `${d}px` })],
    [/^ml-(\d+)$/, ([, d]) => ({ "margin-left": `${d}px` })],
    [/^mtb-(\d+)$/, ([, d]) => ({ "margin-top": `${d}px`, "margin-bottom": `${d}px` })],
    [/^mlr-(\d+)$/, ([, d]) => ({ "margin-left": `${d}px`, "margin-right": `${d}px` })],
    [/^p-(\d+)$/, ([, d]) => ({ padding: `${d}px` })],
    [/^pt-(\d+)$/, ([, d]) => ({ "padding-top": `${d}px` })],
    [/^pr-(\d+)$/, ([, d]) => ({ "padding-right": `${d}px` })],
    [/^pb-(\d+)$/, ([, d]) => ({ "padding-bottom": `${d}px` })],
    [/^pl-(\d+)$/, ([, d]) => ({ "padding-left": `${d}px` })],
    [/^ptb-(\d+)$/, ([, d]) => ({ "padding-top": `${d}px`, "padding-bottom": `${d}px` })],
    [/^plr-(\d+)$/, ([, d]) => ({ "padding-left": `${d}px`, "padding-right": `${d}px` })],
    [/^br-(\d+)$/, ([, d]) => ({ "border-radius": `${d}px` })],
    [/^img-(\d+)$/, ([, d]) => ({ width: `${d}px`, height: `${d}px` })],
    [/^imgc-(\d+)$/, ([, d]) => ({ width: `${d}px`, height: `${d}px`, "border-radius": "50%" })]
  ],
  shortcuts: {
    clickable: "cursor-pointer select-none",
    ellipsis: "text-truncate",
    full: "w-full h-full",
    "flex-col": "flex flex-col",
    "f-c-c": "flex justify-center items-center"
  }
});
