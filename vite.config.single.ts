import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
import UnoCSS from "unocss/vite";
import path from "path";
import legacyPlugin from "@vitejs/plugin-legacy";
import { ElementPlusResolver } from "unplugin-vue-components/resolvers";
import AutoImport from "unplugin-auto-import/vite";
import Components from "unplugin-vue-components/vite";
import { visualizer } from "rollup-plugin-visualizer";
import { viteSingleFile } from "vite-plugin-singlefile";

const pathSrc = path.resolve(__dirname, "src");
export default defineConfig({
  base: "./",
  resolve: {
    extensions: [".ts", ".vue", "js", ".json", ".ejs", ".mjs"],
    alias: [
      {
        find: "@",
        replacement: pathSrc
      }
    ]
  },
  css: {
    preprocessorOptions: {
      // 定义全局 SCSS 变量
      scss: {
        api: "modern-compiler",
        additionalData: `
          @use "@/styles/variables.scss" as *;
        `
      }
    }
  },
  plugins: [
    vue(),
    viteSingleFile(),
    legacyPlugin({
      targets: [
        "Android > 39",
        "Chrome >= 60",
        "Safari >= 10.1",
        "iOS >= 10.3",
        "Firefox >= 54",
        "Edge >= 15"
      ]
    }),
    AutoImport({
      imports: ["vue", "vue-router", "pinia"],
      dts: "src/auto-imports.d.ts",
      resolvers: [
        // 导入 Element Plus函数，如：ElMessage, ElMessageBox 等
        ElementPlusResolver()
      ]
    }),
    Components({
      dirs: ["src/views/**/components", "src/components", "src/components-biz"],
      dts: "src/components.d.ts",
      resolvers: [
        // 导入 Element Plus 组件
        ElementPlusResolver()
      ]
    }),
    UnoCSS(),
    visualizer()
  ],
  server: {
    port: 8800,
    host: "0.0.0.0",
    open: true,
    // proxy: {
    //   "/digit-train-plan/api": {
    //     // target: "https://dev.bwton.cn/digit-dev01/",
    //     changeOrigin: true
    //   }
    // },
    headers: {
      "Access-Control-Allow-Origin": "*"
    }
  },
  build: {
    sourcemap: false,
    chunkSizeWarningLimit: 2000, // 消除打包大小超过500kb警告
    minify: "terser", // Vite 2.6.x 以上需要配置 minify: "terser", terserOptions 才能生效
    terserOptions: {
      compress: {
        keep_infinity: true, // 防止 Infinity 被压缩成 1/0，这可能会导致 Chrome 上的性能问题
        drop_console: true, // 生产环境去除 console
        drop_debugger: true // 生产环境去除 debugger
      },
      format: {
        comments: false // 删除注释
      }
    }
    // rollupOptions: {
    //   external: ["echarts"],
    //   output: {
    //     globals: {
    //       echarts: "echarts"
    //     }
    //   }
    // }
  }
});
