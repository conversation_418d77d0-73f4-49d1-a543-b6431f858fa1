import { createRouter, createWebHashHistory, type RouteRecordRaw } from "vue-router";
import type { App } from "vue";
import ReportPage from "@/views/report/index.vue";

const routes: RouteRecordRaw[] = [
  {
    path: "/report",
    component: ReportPage,
    meta: {
      title: ""
    }
  },
  {
    path: "/:pathMatch(.*)*",
    redirect: "/report"
  }
];

const router = createRouter({
  history: createWebHashHistory(),
  routes: [
    {
      path: "/",
      redirect: "/report"
    },
    ...routes
  ]
});

export const setupRouter = (app: App<Element>) => {
  app.use(router);
};

export default router;
