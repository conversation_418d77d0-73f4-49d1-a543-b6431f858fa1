interface SlideState {
  currentSlide: number;
  totalSlideCount: number;
  loadedMaps: string[];
}

export const useSlideStore = defineStore("slide", {
  state: (): SlideState => {
    return {
      currentSlide: 0,
      totalSlideCount: 0,
      loadedMaps: []
    };
  },
  getters: {
    getCurrentSlide(state: SlideState) {
      return state.currentSlide;
    }
  },
  actions: {
    setTotalSlideCount(count: number) {
      this.totalSlideCount = count;
    },
    setLoadMaps(name: string) {
      this.loadedMaps.push(name);
      // 去重
      this.loadedMaps = Array.from(new Set(this.loadedMaps));
    },
    actionPrevSlide() {
      this.currentSlide = Math.max(this.currentSlide - 1, 0);
    },
    actionNextSlide() {
      this.currentSlide = Math.min(this.currentSlide + 1, this.totalSlideCount - 1);
    },
    actionJumpToSlide(slide: number) {
      this.currentSlide = slide;
    }
  }
});
