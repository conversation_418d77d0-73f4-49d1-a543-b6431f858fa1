import { UserLoginType, UserInfoType } from "@/apis/login/types";
import router from "@/router";
import { store } from "../index";

interface UserState {
  token: string | null;
  userInfo: UserInfoType | undefined;
  loginInfo?: UserLoginType;
  rememberMe: boolean;
}

export const useUserStore = defineStore("user", {
  state: (): UserState => {
    return {
      token: null,
      userInfo: undefined,
      rememberMe: false,
      loginInfo: undefined
    };
  },
  getters: {
    getUserInfo(): UserInfoType | undefined {
      return this.userInfo;
    },
    getLoginInfo(): UserLoginType | undefined {
      return this.loginInfo;
    },
    getRememberMe(): boolean {
      return this.rememberMe;
    },
    getToken(): string | null {
      return this.token;
    }
  },
  actions: {
    setToken(token: string) {
      this.token = token;
    },
    setUserInfo(userInfo?: UserInfoType) {
      this.userInfo = userInfo;
    },
    logout() {
      this.setToken("");
      this.setUserInfo(undefined);
      router.replace("/login");
    },
    setRememberMe(rememberMe: boolean) {
      this.rememberMe = rememberMe;
    },
    setLoginInfo(loginInfo: UserLoginType | undefined) {
      this.loginInfo = loginInfo;
    }
  },
  persist: true
});

export const useUserStoreWithOut = () => {
  return useUserStore(store);
};
