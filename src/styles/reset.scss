*,
::before,
::after {
  box-sizing: border-box;
  border-color: currentcolor;
  border-style: solid;
  border-width: 0;
}

#app {
  width: 100%;
  height: 100%;
}

html {
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  line-height: 1.5;
  tab-size: 4;
  text-size-adjust: 100%;
}

body {
  width: 100%;
  height: 100%;
  margin: 0;
  font-family:
    "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑",
    Arial, sans-serif;
  line-height: inherit;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizelegibility;
}

a {
  color: inherit;
  text-decoration: inherit;
}

img,
svg {
  display: inline-block;
}

svg {
  // 因icon大小被设置为和字体大小一致，而span等标签的下边缘会和字体的基线对齐，故需设置一个往下的偏移比例，来纠正视觉上的未对齐效果
  vertical-align: -0.15em;
}

ul,
li {
  padding: 0;
  margin: 0;
  list-style: none;
}

*,
*::before,
*::after {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

a,
a:focus,
a:hover {
  color: inherit;
  text-decoration: none;
  cursor: pointer;
}

a:focus,
a:active,
div:focus {
  outline: none;
}

.c-content {
  color: #4b5563;
}
.c-primary {
  color: #b1fff2;
}

.bg-primary {
  background-color: var(--el-color-primary);
}
