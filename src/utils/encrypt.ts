import CryptoJS from "crypto-js";

export default {
  // md5 序列化
  getmd5(str: string) {
    return str ? CryptoJS.MD5(str) : "";
  },
  // 加密
  encrypt(str: string, key?: string) {
    if (!str || typeof str !== "string") return "";
    try {
      let keyStr = key ? key : "bwextra         "; // 必须是8的倍数
      let wordStr = CryptoJS.enc.Utf8.parse(str); // 得到的是16位的 wordArray
      let keyCode = CryptoJS.enc.Utf8.parse(keyStr); // toString 将wordArray 转为长度为16的字符串
      // 采用ECB模式，不需要偏移量vi
      let encrypted = CryptoJS.AES.encrypt(wordStr, keyCode, {
        mode: CryptoJS.mode.ECB,
        padding: CryptoJS.pad.ZeroPadding
      });
      return CryptoJS.enc.Base64.stringify(encrypted.ciphertext);
    } catch (error) {
      console.log(error);
      return "";
    }
  },
  // 解密
  decrypt(code: string, key: string) {
    if (!code || typeof code !== "string") return "";
    try {
      let base64 = CryptoJS.enc.Base64.parse(code);
      let content = CryptoJS.enc.Base64.stringify(base64);
      let keyStr = key ? key : "bwextra         ";
      let keyCode = CryptoJS.enc.Utf8.parse(keyStr);
      let result = CryptoJS.AES.decrypt(content, keyCode, {
        mode: CryptoJS.mode.ECB,
        padding: CryptoJS.pad.ZeroPadding
      });
      return result.toString(CryptoJS.enc.Utf8);
    } catch (error) {
      console.log(error);
      return "";
    }
  }
};
