import axios from "axios";
import { ElMessage } from "element-plus";
import type { AxiosInstance, AxiosRequestConfig } from "axios";
import { useUserStore, useUserStoreWithOut } from "@/store/modules/user";

export const baseUrl = `${import.meta.env.VITE_API_URL}digit-train-plan/api`;

const createInstance = () => {
  const instance = axios.create();

  instance.interceptors.request.use(
    // 发送之前
    (config) => config,
    // 发送失败
    (error) => Promise.reject(error)
  );

  instance.interceptors.response.use(
    (response) => {
      const { errcode, errmsg, result } = response.data;
      switch (errcode) {
        case "0000":
          return result;
        case "0005":
          const store = useUserStoreWithOut();
          store.logout();
          window.location.href = "#/login";
          return Promise.reject(result);

        default:
          ElMessage({
            message: errmsg || "请求出错～",
            type: "error",
            duration: 3 * 1000,
            showClose: true
          });
          return Promise.reject(response);
      }
    },
    (error) => {
      console.log("请求出错～", error);
      ElMessage({
        message: "请求出错～",
        type: "error",
        duration: 3 * 1000,
        showClose: true
      });
    }
  );

  return instance;
};

const createRequest = (instance: AxiosInstance) => {
  return <T>(option: AxiosRequestConfig): Promise<T> => {
    const userStore = useUserStore();
    const token = userStore.getToken;

    const { headers, ...reset } = option;

    const defaultConfig: AxiosRequestConfig = {
      baseURL: baseUrl,
      headers: {
        adminToken: token ?? undefined,
        "Content-Type": "application/json; charset=UTF-8",
        ...headers
      },
      data: {},
      timeout: 10000,
      withCredentials: false
    };

    return instance({ ...defaultConfig, ...reset });
  };
};

const instance = createInstance();

export default createRequest(instance);
