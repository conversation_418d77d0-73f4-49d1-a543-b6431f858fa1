export interface UserLoginType {
  user_name: string;
  password: string;
}

interface UserLoginInfo {
  user_id: number;
  user_name: string;
}

interface CompanyInfo {
  company_name: string;
  company_id: number;
  esp_comp_no: string;
}

export interface UserInfoType {
  user_login_info: UserLoginInfo;
  company_info: CompanyInfo;
}

export interface UserLoginResponse extends UserInfoType {
  token: string;
}
