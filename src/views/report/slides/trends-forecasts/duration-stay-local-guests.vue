<template>
  <layout-main>
    <div class="w-full h-full relative">
      <bds-map
        ref="durationStayLocalGuestsMap"
        :showBubble="true"
        show-name
        :bubble-data="bubbleData"
        @load="handleMapLoad"
      />

      <!-- 右侧数据面板 title="到访客群行为分析" -->
      <div class="data-panel z-2000" v-if="isShowDataPanel && stayDurationData">
        <div class="data-panel-title">到访客群行为分析</div>
        <card-slot card-type="cyan" :width="470" :height="465">
          <div class="chart-content">
            <!-- 商圈选择器 -->
            <district-selector
              :default-active="activeDistrict"
              @district-change="handleDistrictChange"
            />

            <!-- 图表组件 -->
            <div class="chart-section">
              <div class="chart-section-title">
                <img src="@/assets/icon.png" alt="chart-title-icon" class="chart-title-icon" />
                <h4 class="chart-title">{{ chartTitle }}</h4>
              </div>
              <p class="chart-desc">
                {{ stayDurationData.page_conclusion || "" }}
              </p>
              <stay-duration-chart :chart-data="currentChartData" :district-name="activeDistrict" />
            </div>
          </div>
        </card-slot>
      </div>
    </div>
  </layout-main>
</template>

<script setup lang="ts">
import CardSlot from "../../components/card-slot.vue";
import DistrictSelector from "../../components/district-selector.vue";
import StayDurationChart from "./components/ring-no-line.vue";
import { SLIDE_COMPONENTS_INDEX } from "@/views/report/config";
import { useSlideHook } from "../../hooks/useSlideHook";
import chartDataJson from "@/data/visitor-behav-analysis/客群停留时长分析.json";
import { toWan } from "@/utils";
import { useSlideStore } from "@/store/modules/slide";
import { BubbleData } from "@/views/report/hooks/useBdsMarkerHook";

interface BusinessDistrict {
  bds_id: string;
  bds_name: string;
  bds_conclusion: string;
  visit_flow: number;
  stay_list: StayDuration[];
  bds_latitude: number;
  bds_longitude: number;
}

interface StayDuration {
  stay_sec: string;
  stay_num: number;
  stay_percentage: number;
}

const slideStore = useSlideStore();

const durationStayLocalGuestsMap = ref<any>(null);
const isShowDataPanel = ref(false);

useSlideHook((slide) => {
  if (slide === SLIDE_COMPONENTS_INDEX["durationStayLocalGuests"]) {
    // 初始化数据
    initStayDurationData();
    nextTick(() => {
      isShowDataPanel.value = true;
    });
  } else {
    isShowDataPanel.value = false;
  }
});

// 响应式数据
const activeDistrict = ref("文化博览中心");
const stayDurationData = ref<any>(null);

// 商圈数据映射
const districtData = computed(() => {
  if (!stayDurationData.value) return {};
  return stayDurationData.value.bds_list.reduce(
    (acc: Record<string, any>, district: any) => {
      acc[district.bds_name] = district;
      return acc;
    },
    {} as Record<string, any>
  );
});

// 当前选中的商圈数据
const currentDistrictData = computed(() => {
  return districtData.value[activeDistrict.value] || null;
});

const chartTitle = ref("本地客群停留时长分析");

// 图表数据
const currentChartData = computed(() => {
  if (!currentDistrictData.value) return [];

  return currentDistrictData.value.stay_list.map((item: StayDuration) => ({
    name: item.stay_sec,
    value: Math.round(item.stay_percentage * 100),
    count: item.stay_num
  }));
});

const getBubbleData = (bdsList: BusinessDistrict[]) => {
  const data = bdsList.sort(
    (a: BusinessDistrict, b: BusinessDistrict) => b.visit_flow - a.visit_flow
  );
  return data.map((item: any, i: number) => {
    const value = toWan(item.visit_flow);
    return {
      bds_id: item.bds_id,
      value: value,
      size: i == 0 ? 100 : i == 1 ? 90 : i == 2 ? 80 : 60, // 前三的大小及颜色各异，后面的大小统一，颜色统一
      position: {
        lat: item.bds_latitude,
        lng: item.bds_longitude
      }
    };
  });
};

const bubbleData = ref<BubbleData[]>([]);

// 处理商圈切换
const handleDistrictChange = (district: string) => {
  activeDistrict.value = district;
};

// 初始化数据
const initStayDurationData = () => {
  stayDurationData.value = chartDataJson.result;
  const { bds_list = [] } = chartDataJson.result;
  bubbleData.value = getBubbleData(bds_list) as BubbleData[];
};

const loaded = ref(false);

const handleMapLoad = (value: boolean) => {
  loaded.value = value;
  slideStore.setLoadMaps("durationStayLocalGuests");
};

defineExpose({
  name: "durationStayLocalGuests",
  preloadMap: () => {
    if (loaded.value) {
      slideStore.setLoadMaps("durationStayLocalGuests");
    } else {
      setTimeout(() => {
        durationStayLocalGuestsMap.value?.initMap("durationStayLocalGuests");
      }, 0);
    }
  }
});
</script>

<style scoped lang="scss">
.map-section {
  width: 100%;
  height: 100%;
}

.data-panel {
  position: absolute;
  top: 30%;
  right: 50px;
  transform: translateY(-30%);
  z-index: 20;
  pointer-events: none;

  .chart-content {
    padding: 20px;
    pointer-events: auto;
  }
}

.data-panel-title {
  font-size: 22px;
  font-weight: 700;
  color: #b1fff2;
  margin: 0 0 30px 0;
  line-height: 22px;
  pointer-events: auto;
}

.chart-section {
  padding: 0 10px;
  .chart-section-title {
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 0 0 10px 0;

    .chart-title-icon {
      width: 18px;
      height: 22px;
    }

    .chart-title {
      font-size: 16px;
      font-weight: 700;
      color: #f7fffe;
      line-height: 1;
      text-shadow: 0px 4px 6px #4d429e;
    }
  }

  .chart-desc {
    font-size: 12px;
    color: #e6e6e6;
    margin: 0 0 20px 0;
    line-height: 18px;
    word-break: break-all;
    text-align: justify;
    text-shadow: 0px 4px 6px #4d429e;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

// 添加覆盖物悬停效果样式
.district-overlay {
  &:hover {
    transform: translateY(-4px) scale(1.1);
    box-shadow:
      0 12px 32px rgba(0, 0, 0, 0.7),
      0 0 0 2px rgba(255, 255, 255, 0.4),
      0 0 40px rgba(147, 244, 255, 0.5);

    .overlay-icon {
      transform: scale(1.15) rotate(8deg);
      box-shadow: 0 0 24px rgba(147, 244, 255, 0.9);

      > div > div {
        opacity: 0.9;
        transform: scale(1.3);
      }
    }

    .overlay-text {
      text-shadow:
        0 4px 8px rgba(0, 0, 0, 1),
        0 0 15px rgba(147, 244, 255, 0.6);
      transform: translateX(3px);
    }
  }

  .overlay-icon {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);

    > div {
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);

      > div {
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      }
    }
  }

  .overlay-text {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  }
}

.retry-btn {
  background: #93f4ff;
  color: #000;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  margin-top: 15px;
  transition: all 0.3s ease;

  &:hover {
    background: #c3fff6;
  }
}
</style>
