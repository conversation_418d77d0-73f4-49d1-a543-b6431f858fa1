<template>
  <layout-main>
    <div class="duration-stay-container">
      <!-- 地图组件：补充areaData用于区域展示 -->
      <!--areaData 新增：传递地铁站点区域数据  -->
      <bds-map
        ref="mapRefForDurationStayLocalGuests"
        :showArea="true"
        show-name
        :area-data="areaData"
        @load="handleMapLoad"
      />
      <!-- 右侧数据面板 -->
      <div class="data-panel" v-if="isShowDataPanel && stayDurationData">
        <div class="data-panel-title">公共交通枢纽分析</div>
        <card-slot card-type="cyan" :width="470" :height="465">
          <div class="chart-content">
            <div class="chart-section">
              <div class="chart-section-title">
                <img src="@/assets/icon.png" alt="chart-title-icon" class="chart-title-icon" />
                <h4 class="chart-title">{{ chartTitle }}</h4>
              </div>
              <p class="chart-desc">
                {{ stayDurationData?.page_conclusion }}
              </p>
              <!-- 无数据兜底 -->
              <div v-if="!chartData.categories.length" class="no-data">暂无地铁站客流数据</div>
              <bar-chart v-else ref="echartsChart" :data="chartData" />
            </div>
          </div>
        </card-slot>
      </div>
    </div>
  </layout-main>
</template>

<script setup lang="ts">
import CardSlot from "../../components/card-slot.vue";
import barChart from "./components/bar-chart.vue";
import { SLIDE_COMPONENTS_INDEX } from "@/views/report/config";
import { useSlideHook } from "../../hooks/useSlideHook";
import chartDataJson from "@/data/visitor-behav-analysis/商圈地铁客流分析.json";
import { useSlideStore } from "@/store/modules/slide";
import subwayIcon from "@/assets/subway-icon.png"; // 根据实际目录结构调整路径

// 类型定义 - 增强类型安全
interface BusinessDistrict {
  bds_id: string;
  bds_name: string;
  bds_conclusion: string;
  station_list: SubwayStation[]; // 地铁站点列表
}

interface SubwayStation {
  station_name: string; // 地铁站名
  station_flow: number; // 客流量
  station_latitude: number; // 纬度
  station_longitude: number; // 经度
}

interface AreaDataItem {
  // 地图区域数据格式（匹配bds-map的area-data要求）
  bds_id: string;
  name: string;
  position: { lat: number; lng: number };
  icon?: string; // 新增 icon 字段，用于存储图标路径
  popupData?: {
    station_name: string;
    station_flow: number;
  };
}

// 组件引用与状态
const mapRefForDurationStayLocalGuests = ref<any>(null);
const echartsChart = ref<any>(null);
const isShowDataPanel = ref(false);
const loaded = ref(false);
const slideStore = useSlideStore();
const stayDurationData = ref<any>(null);
const chartTitle = ref("商圈周边地铁站客流分布");

/**
 * 工具函数：将数字转换为“万”为单位，并保留两位小数
 * @param num - 原始客流量（如 21419800.02）
 * @returns 转换后数字（如 2142.00）
 */
const formatToWan = (num: number): number => {
  return Number((num / 10000).toFixed(2));
};

// 计算属性：处理图表数据（从JSON提取station_name和station_flow）
const chartData = computed<{ categories: string[]; values: number[] }>(() => {
  if (!stayDurationData.value?.bds_list) return { categories: [], values: [] };

  // 从bds_list中提取地铁站点数据，过滤无效项
  const validStations = stayDurationData.value.bds_list
    .map((bds) => bds.station_list[0]) // 每个商圈对应1个地铁站
    .filter((station) => station?.station_name && station.station_flow !== undefined);

  return {
    // categories：提取station_name
    categories: validStations.map((station) => station.station_name),
    // values：提取station_flow并转换为万单位（保留两位小数）
    values: validStations.map((station) => formatToWan(station.station_flow))
  };
});

// 计算属性：处理地图区域数据（匹配bds-map的area-data格式）
const areaData = computed<AreaDataItem[]>(() => {
  if (!stayDurationData.value?.bds_list) return [];

  return stayDurationData.value.bds_list.map((bds) => {
    const station = bds.station_list[0];
    return {
      bds_id: bds.bds_id,
      name: station?.station_name || bds.bds_name,
      position: {
        lat: station?.station_latitude || 0,
        lng: station?.station_longitude || 0
      },
      icon: subwayIcon, // 这里替换为实际的图标路径
      popupData: {
        station_name: station?.station_name || "", // 明确传递站点名
        station_flow: formatToWan(station?.station_flow || 0) // 客流数据（已转“万”单位）
      }
    };
  });
});

// 幻灯片切换逻辑
useSlideHook((slide) => {
  if (slide === SLIDE_COMPONENTS_INDEX["publicTransportation"]) {
    isShowDataPanel.value = true;
    initStayDurationData();

    // 确保DOM和数据更新后初始化图表
    nextTick(() => {
      setTimeout(() => {
        if (echartsChart.value && chartData.value.categories.length) {
          echartsChart.value.initChart();
        }
      }, 100);
    });
  } else {
    isShowDataPanel.value = false;
    // 销毁图表实例，释放资源
    if (echartsChart.value?.chartInstance()) {
      echartsChart.value.chartInstance().dispose();
    }
  }
});

// 初始化数据：从JSON加载数据
const initStayDurationData = () => {
  stayDurationData.value = chartDataJson.result;
};

// 组件挂载时初始化
onMounted(() => {});

// 地图加载回调
const handleMapLoad = (value: boolean) => {
  loaded.value = value;
  slideStore.setLoadMaps("publicTransportation");
};

// 暴露组件方法
defineExpose({
  name: "publicTransportation",
  preloadMap: () => {
    if (loaded.value) {
      slideStore.setLoadMaps("publicTransportation");
    } else {
      setTimeout(() => {
        mapRefForDurationStayLocalGuests.value?.initMap();
      }, 0);
    }
  }
});
</script>

<style scoped lang="scss">
.duration-stay-container {
  position: relative;
  width: 100%;
  height: 100%;
  background-color: #000;
}

.data-panel {
  position: absolute;
  top: 30%;
  right: 50px;
  transform: translateY(-30%);
  z-index: 20;
  pointer-events: none;

  .chart-content {
    padding: 20px;
    pointer-events: auto;
  }
}

.data-panel-title {
  font-size: 22px;
  font-weight: 700;
  color: #b1fff2;
  margin: 0 0 30px 0;
  line-height: 22px;
  pointer-events: auto;
}

.chart-section {
  padding: 0 10px;

  .chart-section-title {
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 0 0 10px 0;

    .chart-title-icon {
      width: 18px;
      height: 22px;
    }

    .chart-title {
      font-size: 16px;
      font-weight: 700;
      color: #f7fffe;
      line-height: 1;
      text-shadow: 0px 4px 6px #4d429e;
    }
  }

  .chart-desc {
    font-size: 12px;
    color: #e6e6e6;
    margin: 0 0 20px 0;
    line-height: 18px;
    word-break: break-all;
    text-align: justify;
    text-shadow: 0px 4px 6px #4d429e;
  }

  // 无数据提示样式
  .no-data {
    color: rgba(255, 255, 255, 0.7);
    text-align: center;
    padding: 60px 0;
    font-size: 14px;
  }
}

// 覆盖物悬停效果
.district-overlay {
  &:hover {
    transform: translateY(-4px) scale(1.1);
    box-shadow:
      0 12px 32px rgba(0, 0, 0, 0.7),
      0 0 0 2px rgba(255, 255, 255, 0.4),
      0 0 40px rgba(147, 244, 255, 0.5);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);

    .overlay-icon {
      transform: scale(1.15) rotate(8deg);
      box-shadow: 0 0 24px rgba(147, 244, 255, 0.9);
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);

      > div > div {
        opacity: 0.9;
        transform: scale(1.3);
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      }
    }

    .overlay-text {
      text-shadow:
        0 4px 8px rgba(0, 0, 0, 1),
        0 0 15px rgba(147, 244, 255, 0.6);
      transform: translateX(3px);
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    }
  }

  .overlay-icon {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);

    > div {
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);

      > div {
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      }
    }
  }

  .overlay-text {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  }
}

.retry-btn {
  background: #93f4ff;
  color: #000;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  margin-top: 15px;
  transition: all 0.3s ease;

  &:hover {
    background: #c3fff6;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
