<template>
  <div class="bubble-chart-container">
    <div class="chart-header">
      <h2 class="chart-title">{{ title }}</h2>
      <p v-if="subtitle" class="chart-subtitle">{{ subtitle }}</p>
    </div>
    <div ref="chartRefScatter" class="chart-content"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue';
import * as echarts from 'echarts';
import { IconUriEnum } from './icon-uri-enum';

/**
 * 气泡图表数据项接口
 */
interface BubbleDataItem {
  /** 数据项名称（如商圈名称） */
  name: string;
  /** 数值（如客流量） */
  value: number;
  /** 分类（如节假日、周末、工作日） */
  category: string;
  /** 自定义颜色（可选） */
  color?: string;
  /** 气泡大小（可选：small、medium、big） */
  size?: string;
}

/**
 * 气泡图表组件属性接口
 */
interface Props {
  /** 图表标题 */
  title?: string;
  /** 图表副标题 */
  subtitle?: string;
  /** 气泡图表数据数组 */
  data?: BubbleDataItem[];
  /** 分类数组（如：['节假日', '周末', '工作日']） */
  categories?: string[];
  /** 颜色数组（对应分类的颜色） */
  colors?: string[];
  /** 自定义 ECharts 配置选项（会与默认配置合并） */
  option?: echarts.EChartsOption;
  /** 图表宽度 */
  width?: string | number;
  /** 图表高度 */
  height?: string | number;
  /** 图表主题 */
  theme?: 'dark' | 'light';
  /** 图表图标Enum */
  iconEnum?: any;
}

/**
 * 组件属性定义，设置默认值
 */
const props = withDefaults(defineProps<Props>(), {
  title: '气泡图表',
  subtitle: '',
  data: () => [],
  categories: () => ['节假日', '周末', '工作日'],
  colors: () => ['#3EECFF', '#B699FF', '#FFFD89'],
  width: '100%',
  height: '400px',
  theme: 'dark',
  iconEnum: () => {}
});

/** 图表容器引用 */
const chartRefScatter = ref<HTMLElement>();
/** ECharts 图表实例 */
let chartInstance: echarts.ECharts | null = null;

/**
 * 生成默认的 ECharts 配置选项
 * @returns ECharts 配置对象
 */
const getDefaultOption = (): echarts.EChartsOption => {
  // 计算所有数值的范围，用于气泡大小映射
  const allValues = props.data.map(item => item.value);
  const minValue = allValues.length > 0 ? Math.min(...allValues) : 0;
  const maxValue = allValues.length > 0 ? Math.max(...allValues) : 10;
  
  // 按类别分组数据，为每个分类创建散点图系列
  const groupedData = props.categories.map((category, index) => {
    // 过滤出当前分类的数据
    const categoryData = props.data.filter(item => item.category === category);

    return {
      name: category,
      type: 'scatter', // 散点图类型，用于显示气泡
      data: categoryData.map(item => {
        // 根据数值计算气泡大小，最小30最大50
        const normalizedValue = (item.value - minValue) / (maxValue - minValue);
        const symbolSize = 30 + normalizedValue * 20; // 30到50的范围
        
        return {
          name: item.name,
          value: [item.name, item.value], // 数据格式：[x轴值, y轴值]
          itemStyle: {
            color: item.color || props.colors[index] // 使用自定义颜色或默认颜色
          },
          symbol: props.iconEnum && props.iconEnum[item.category] ? (IconUriEnum as any)[props.iconEnum[item.category]] : 'circle', // 使用自定义图标
          symbolSize: symbolSize, // 根据数值自动计算气泡大小
        };
      }),
    };
  });

  // 为每个x轴位置添加竖线系列
  const uniqueNames = Array.from(new Set(props.data.map(item => item.name)));
  const lineSeries = uniqueNames.map(name => {
    // 获取该位置的所有数据点
    const locationData = props.data.filter(item => item.name === name);
    if (locationData.length === 0) return undefined;
    
    // 使用数据中的最大值作为竖线高度
    const yAxisMax = allValues.length > 0 ? Math.max(...allValues) + 1000 : 0;
    
    return {
      name: `${name}_line`,
      type: 'line',
      data: [
        [name, 0], // 起点：x轴位置，y值为0
        [name, yAxisMax] // 终点：x轴位置，y值为y轴最大值
      ],
      lineStyle: {
        color: 'rgba(255, 255, 255, 0.2)', // 半透明白色
        width: 1,
        type: 'solid'
      },
      symbol: 'none', // 不显示端点标记
      silent: true, // 不响应鼠标事件
      z: 1 // 确保线条在气泡下方
    };
  }).filter((item): item is NonNullable<typeof item> => item !== undefined); // 过滤掉undefined值

  // 合并气泡系列和线条系列
  const allSeries = [...groupedData, ...lineSeries] as echarts.SeriesOption[];
  
  return {
    backgroundColor: 'transparent', // 透明背景
    title: {
      show: false // 隐藏默认标题
    },
    tooltip: {
      trigger: 'item', // 鼠标悬停触发
      backgroundColor: 'rgba(31, 26, 34, 0.9)', // 深色背景
      borderColor: 'rgba(31, 26, 34, 0.9)',
      borderWidth: 1,
      textStyle: {
        color: '#fff',
        fontSize: 12
      },
      /**
       * 自定义 tooltip 内容格式化函数
       * @param params tooltip 参数
       * @returns 格式化的 HTML 字符串
       */
      formatter: (params: any) => {
        if (!params || !params.data) return '';
        
        const data = params.data;
        const category = params.seriesName;
        const color = params.color;
        // 数值格式化：大于10000显示为"万"单位
        const value = data.value[1] >= 10000 
          ? (data.value[1] / 10000).toFixed(2) + '万'
          : data.value[1].toString();
        
        return `
          <div>
            <div style="font-size: 12px; font-weight: 400; color: #fff;">${data.name}</div>
            <div style="display: flex; justify-content: space-between; align-items: center; gap: 10px;">
              <div style="width: 20px; height: 4px; background-color: ${color};"></div>
              <div style="font-size: 12px; font-weight: 400; color: #fff;">${category}</div>
              <div style="font-size: 12px; font-weight: 400; color: ${color};">${value}</div>
            </div>
          </div>
        `;
      }
    },
    legend: {
      top: 10, // 距离顶部距离
      right: 20, // 距离右侧距离
      textStyle: {
        color: '#fff',
        fontSize: 12
      },
      itemWidth: 8, // 图例标记宽度
      itemHeight: 8, // 图例标记高度
      data: props.categories.map((category, index) => ({
        name: category,
        icon: props.iconEnum && props.iconEnum[category] ? (IconUriEnum as any)[props.iconEnum[category]] : 'circle' // 使用自定义图标
      })),
    },
    grid: {
      top: 60, // 距离顶部距离
      left: 0, // 距离左侧距离
      right: 10, // 距离右侧距离
      bottom: 30, // 距离底部距离
      containLabel: true // 包含坐标轴标签
    },
    xAxis: {
      type: 'category', // 分类轴
      data: Array.from(new Set(props.data.map(item => item.name))), // 去重后的商圈名称
      axisLine: {
        show: true,
        lineStyle: {
          color: '#8490A9' // 轴线颜色
        }
      },
      axisTick: {
        show: false // 隐藏刻度线
      },
      axisLabel: {
        color: 'rgba(255, 255, 255, 0.7)', // 标签颜色
        fontSize: 12
      },
    },
    yAxis: {
      type: 'value', // 数值轴
      nameTextStyle: {
        color: 'rgba(255, 255, 255, 0.7)',
        fontSize: 12
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: '#8490A9' // 轴线颜色
        }
      },
      axisTick: {
        show: false // 隐藏刻度线
      },
      splitLine: {
        show: false // 隐藏分割线
      },
      axisLabel: {
        color: 'rgba(255, 255, 255, 0.7)', // 标签颜色
        fontSize: 12,
        /**
         * Y轴标签格式化函数
         * @param value 数值
         * @returns 格式化后的字符串
         */
        formatter: (value: number) => {
          if (value >= 10000) {
            return (value / 10000).toFixed(1) + '万';
          }
          return value.toString();
        }
      }
    },
    series: allSeries // 图表系列数据
  };
};

/**
 * 初始化图表
 */
const initChart = () => {
  if (!chartRefScatter.value) return;

  chartInstance = echarts.init(chartRefScatter.value, props.theme, { renderer: 'svg' });
  
  // 合并用户配置和默认配置
  const finalOption = props.option 
    ? echarts.util.merge(getDefaultOption(), props.option)
    : getDefaultOption();
    
  chartInstance.setOption(finalOption);
};

/**
 * 更新图表
 */
const updateChart = () => {
  if (!chartInstance) return;
  
  const finalOption = props.option 
    ? echarts.util.merge(getDefaultOption(), props.option)
    : getDefaultOption();
    
  chartInstance.setOption(finalOption, true);
};

/**
 * 监听数据变化，自动更新图表
 */
watch(
  () => [props.data, props.categories, props.colors, props.option],
  () => {
    nextTick(() => {
      updateChart();
    });
  },
  { deep: true }
);

/**
 * 组件挂载时初始化图表
 */
onMounted(() => {

});

/**
 * 组件卸载时清理资源
 */
onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }
});

/**
 * 暴露方法给父组件
 */
defineExpose({
  /** 获取图表实例 */
  getChartInstance: () => chartInstance,
  /** 调整图表尺寸 */
  resize: () => chartInstance?.resize(),
  /** 设置图表配置 */
  setOption: (option: echarts.EChartsOption) => chartInstance?.setOption(option),
  /** 初始化图表 */
  initChart: () => initChart()
});
</script>

<style lang="scss" scoped>
.bubble-chart-container {
  width: v-bind(width);
  height: v-bind(height);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;

  .chart-header {
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .chart-title {
      color: #B1FFF2;
      font-size: 22px;
      font-weight: 700;
      margin: 0 0 12px 0;
      line-height: 1.2;
    }

    .chart-subtitle {
      max-width: 710px;
      height: 55px;
      color: #FFFFFF;
      font-size: 14px;
      font-weight: 400;
      white-space: normal;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      margin: 0;
      line-height: 18px;
    }
  }
  
  .chart-content {
    width: 100%;
    height: calc(100% - 60px);
  }
}
</style>
