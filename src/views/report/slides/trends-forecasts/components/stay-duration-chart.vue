<template>
  <div class="chart-container" style="pointer-events: auto;">
    <div ref="chartRef" class="chart-canvas"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'

interface ChartDataItem {
  name: string
  value: number
  count: number
}

interface Props {
  chartData: ChartDataItem[]
  districtName: string
}

const props = defineProps<Props>()

const chartRef = ref<HTMLElement>()
let chartInstance: echarts.ECharts | null = null

// 颜色配置 - 根据图片中的颜色调整
const colors = [
  '#39F2DC',
  '#FFFC62',
  '#3691FF',
  '#BD46F0',
  '#D1763D',
  '#4B54FE'
]

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return
  
  chartInstance = echarts.init(chartRef.value, null, { renderer: 'svg' })
  updateChart()
}

// 更新图表数据
const updateChart = () => {
  if (!chartInstance || !props.chartData.length) return

  const option = {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'item',
      formatter: (params: any) => {
        const data = params.data
        return `${data.name}<br/>占比: ${data.value.toFixed(1)}%<br/>人数: ${data.count}人`
      },
      backgroundColor: 'rgba(0, 0, 0, 0.9)',
      borderColor: '#93F4FF',
      borderWidth: 2,
      borderRadius: 6,
      textStyle: {
        color: '#ffffff',
        fontSize: 12,
        fontWeight: 500
      },
      padding: [6, 10]
    },

    series: [
      {
        name: '停留时长',
        type: 'pie',
        radius: ['45%', '60%'],
        center: ['50%', '50%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 12,
          borderColor: 'transparent',
          borderWidth: 0
        },
        markPoint:{
          symbol: 'rect',
        },
        label: {
          show: true,
          position: 'outside',
          formatter: (params: any) => {
            return `${params.name}\n${params.value.toFixed(1)}%`
          },
          textStyle: {
            color: '#ffffff',
            fontSize: 12,
            fontWeight: 400
          }
        },
        labelLine: {
          show: false,
        },
        data: props.chartData.map((item, index) => ({
          name: item.name,
          value: item.value,
          count: item.count,
          label: {
            show: true,
            position: 'outside',
            formatter: `{dot|●} ${item.name}\n${item.value.toFixed(1)}%`,
            rich: {
              dot: {
                color: colors[index % colors.length],
                fontSize: 8,
                padding: [0, 4, 0, 0]
              }
            },
            textStyle: {
              color: '#ffffff',
              fontSize: 12,
              fontWeight: 400
            }
          },
          itemStyle: {
            color: {
              type: 'radial',
              x: 0,
              y: 1,
              r: 5,
              colorStops: [
                {
                  offset: 0,
                  color: colors[index % colors.length] // 中心色
                },
                {
                  offset: 0.5,
                  color: 'rgba(0, 0, 0, 0.1)' // 渐变到中心色的透明度20%
                }
              ]
            }
          }
        }))
      }
    ]
  }

  chartInstance.setOption(option, true)
}

// 监听数据变化
watch(() => props.chartData, () => {
  nextTick(() => {
    updateChart()
  })
}, { deep: true })

// 监听窗口大小变化
const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

onMounted(() => {
  initChart()
  window.addEventListener('resize', handleResize)
})

// 组件卸载时清理
onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
  }
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped lang="scss">
.chart-container {
  width: 100%;
  height: 280px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-canvas {
  width: 100%;
  height: 100%;
}
</style>
