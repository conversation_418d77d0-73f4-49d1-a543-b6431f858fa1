<template>
  <div class="chart-container">
    <div ref="chartRef" class="chart-wrapper"></div>
  </div>
</template>

<script setup lang="ts">
import * as echarts from "echarts";
import type { ECharts, EChartsOption } from "echarts";

// 定义图表数据类型
interface ChartData {
  categories: string[];
  values: number[];
}

// 颜色配置项类型
interface ColorConfig {
  color: string; // 改为直接存储纯色
}

// 组件属性
const props = defineProps<{
  // 图表核心数据（必传，结构与父组件对齐）
  data: ChartData;
  // 图表宽度（可选，默认100%）
  width?: string;
  // 图表高度（可选，默认278px）
  height?: string;
  // 是否启用动画（可选，默认true）
  animation?: boolean;
  // 统一Y轴最大值（可选，用于多图表刻度对齐）
  maxValue?: number;
}>();

// 默认属性
const defaultData: ChartData = {
  categories: [],
  values: []
};

// 颜色数组 - 改为纯色配置
const colorArray: ColorConfig[] = [
  { color: "#FF90B2" },
  { color: "#FFE995" },
  { color: "#FAFFC3" },
  { color: "#C3FFF6" },
  { color: "#B1FFBA" },
  { color: "#C3FFF6" },
  { color: "#C3E3FF" },
  { color: "#D3C3FF" }
];

// 图表引用
const chartRef = ref<HTMLDivElement>(null);
let chartInstance: ECharts | null = null;

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return;

  // 销毁已有实例
  if (chartInstance) {
    chartInstance.dispose();
  }

  // 创建新实例
  chartInstance = echarts.init(chartRef.value);

  // 设置图表选项
  const option = getChartOption();
  chartInstance.setOption(option);

  // 组件卸载时清理
  onUnmounted(() => {});
};

// 获取图表配置选项
const getChartOption = (): EChartsOption => {
  //   const chartData = props.data || defaultData;
  const { categories, values } = props.data; // 直接解构整合后的数据
  const maxValue = props.maxValue || Math.max(...values) * 1.2;

  return {
    tooltip: {
      show: true,
      trigger: "axis",
      axisPointer: {
        type: "shadow",
        shadowStyle: {
          color: "rgba(255, 255, 255, 0.1)"
        }
      },
      formatter: "{b}: {c}",
      backgroundColor: "rgba(14, 42, 67, 0.9)",
      borderColor: "rgba(255, 255, 255, 0.2)",
      borderWidth: 1,
      textStyle: {
        color: "#fff",
        fontSize: 14
      },
      padding: 10
    },
    grid: {
      left: "3%",
      top: "0%",
      right: "10%",
      bottom: "8%",
      containLabel: true
    },
    xAxis: {
      type: "value",
      show: false,
      position: "top",
      max: maxValue,
      splitNumber: 5,
      axisLine: {
        show: false,
        lineStyle: {
          color: "rgba(255, 255, 255, 0.1)"
        }
      },
      axisTick: {
        show: false
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: "rgba(255, 255, 255, 0.05)"
        }
      },
      axisLabel: {
        show: true,
        color: "rgba(255, 255, 255, 0.6)",
        fontSize: 12
      }
    },
    yAxis: {
      type: "category",
      inverse: true,
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      splitLine: {
        show: false
      },
      axisLabel: {
        show: true,
        color: "#FFFFFF",
        fontSize: 14,
        padding: [0, 10, 0, 0]
      },
      data: categories
    },
    series: [
      {
        name: "能耗值",
        type: "bar",
        barWidth: 10,
        barGap: "0%",
        barCategoryGap: "40%",
        data: values,
        label: {
          show: true,
          position: "right",
          formatter: "{c}万人",
          distance: 10,
          textStyle: {
            color: "white",
            fontSize: 13,
            fontWeight: "500"
          }
        },
        itemStyle: {
          // 改为直接使用纯色
          color: function (params) {
            const num = colorArray.length;
            return colorArray[params.dataIndex % num].color;
          },
          barBorderRadius: [10, 10, 10, 10],
          borderWidth: 0.5,
          borderColor: "rgba(255, 255, 255, 0.2)"
        }
      }
    ]
  };
};

// 监听数据变化，更新图表
watch(
  () => props.data,
  () => {
    if (chartInstance) {
      chartInstance.setOption(getChartOption());
    }
  },
  { deep: true }
);

// 监听尺寸变化
watch(
  () => [props.width, props.height],
  () => {
    // chartInstance?.resize();
  }
);

// 组件挂载时初始化图表
onMounted(() => {
  //   initChart();
});

// 组件卸载时销毁图表
onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }
});
defineExpose({
  initChart: () => initChart(),
  chartInstance: () => chartInstance
});
</script>

<style scoped>
.chart-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.chart-wrapper {
  width: 100%;
  height: 278px;
}
</style>
