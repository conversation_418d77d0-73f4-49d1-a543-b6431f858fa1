<template>
  <layout-main>
    <div class="duration-stay-container">
      <bds-map
        ref="mapRefForDurationStayLocalGuests"
        :showArea="true"
        show-name
        :area-data="areaData"
        @load="handleMapLoad"
      />

      <div class="data-panel" v-if="isShowDataPanel && stayDurationData">
        <div class="data-panel-title">公共交通枢纽分析</div>
        <card-slot card-type="cyan" :width="470" :height="465">
          <div class="chart-content">
            <div class="chart-section">
              <div class="chart-section-title">
                <img src="@/assets/icon.png" alt="chart-title-icon" class="chart-title-icon" />
                <h4 class="chart-title">{{ chartTitle }}</h4>
              </div>
              <p class="chart-desc">
                {{ stayDurationData.page_conclusion || "" }}
              </p>
              <!-- 无数据兜底 -->
              <div v-if="!chartData.length" class="no-data">暂无地铁客群户籍分布数据</div>
              <BubbleChart
                v-else
                ref="chartRefDuringHoliday"
                :data="chartData"
                :categories="categories"
                :colors="colors"
                width="100%"
                height="400px"
                theme="dark"
                :iconEnum="registered"
                :enable-data-zoom="true"
              />
            </div>
          </div>
        </card-slot>
      </div>
    </div>
  </layout-main>
</template>

<script setup lang="ts">
import CardSlot from "../../components/card-slot.vue";
import BubbleChart from "./components/bubble-chart.vue";
import { registered } from "./components/icon-uri-enum";
import { SLIDE_COMPONENTS_INDEX } from "@/views/report/config";
import { useSlideHook } from "../../hooks/useSlideHook";
import chartDataJson from "@/data/visitor-behav-analysis/商圈地铁客流来源分析.json";
import { useSlideStore } from "@/store/modules/slide";
import subwayIcon from "@/assets/subway-icon.png"; // 根据实际目录结构调整路径

// 类型定义
interface BusinessDistrict {
  bds_id: string;
  bds_name: string;
  bds_conclusion: string;
  station_list: SubwayStation[];
}
interface SubwayStation {
  station_name: string;
  local_visitor_count: number;
  non_local_visitor_count: number;
  station_latitude: number;
  station_longitude: number;
}
interface AreaDataItem {
  bds_id: string;
  name: string;
  position: { lat: number; lng: number };
  popupData?: {
    station_name: string;
    local_visitor_count: number;
    non_local_visitor_count: number;
  };
}
interface BubbleChartData {
  name: string;
  value: number;
  category: "本地" | "外地";
}
// 预处理数据结构
interface ProcessedStationData {
  [key: string]: {
    local: number;
    nonLocal: number;
    latitude: number;
    longitude: number;
  };
}

// 状态与引用
const mapRefForDurationStayLocalGuests = ref<any>(null);
const chartRefDuringHoliday = ref<any>(null);
const isShowDataPanel = ref(false);
const slideStore = useSlideStore();
const loaded = ref(false);
const stayDurationData = ref<any>(null);
const chartTitle = ref("商圈周边地铁出行客群户籍分布");

// 固定配置
const categories = ref(["本地", "外地"]);
const colors = ref(["#3EECFF", "#B699FF"]);

// 预处理数据存储
const processedData = ref<ProcessedStationData>({});

// 初始化图表数据
const initChartData = () => {
  // 存储原始数据
  stayDurationData.value = chartDataJson.result || {};

  // 预处理所有站点数据
  if (stayDurationData.value?.bds_list) {
    stayDurationData.value.bds_list.forEach((district: BusinessDistrict) => {
      const station = district.station_list[0] || {};
      if (station.station_name) {
        processedData.value[station.station_name] = {
          local: station.local_visitor_count || 0,
          nonLocal: station.non_local_visitor_count || 0,
          latitude: station.station_latitude || 0,
          longitude: station.station_longitude || 0
        };
      }
    });
  }
};

// 构建气泡图表数据
const buildBubbleChartData = (): BubbleChartData[] => {
  const bubbleData: BubbleChartData[] = [];

  if (!stayDurationData.value?.bds_list) {
    return bubbleData;
  }

  stayDurationData.value.bds_list.forEach((district: BusinessDistrict) => {
    const station = district.station_list[0];
    if (!station?.station_name) return;

    // 本地数据
    bubbleData.push({
      name: station.station_name,
      value: processedData.value[station.station_name]?.local || 0,
      category: "本地"
    });

    // 外地数据
    bubbleData.push({
      name: station.station_name,
      value: processedData.value[station.station_name]?.nonLocal || 0,
      category: "外地"
    });
  });

  return bubbleData;
};

// 动态提取图表数据（使用函数调用方式，与参考代码风格统一）
const chartData = computed<BubbleChartData[]>(() => {
  return buildBubbleChartData();
});
/**
 * 工具函数：将数字转换为“万”为单位，并保留两位小数
 * @param num - 原始客流量（如 21419800.02）
 * @returns 转换后数字（如 2142.00）
 */
const formatToWan = (num: number): number => {
  return Number((num / 10000).toFixed(2));
};
// 地图区域数据
const areaData = computed<AreaDataItem[]>(() => {
  if (!stayDurationData.value?.bds_list) return [];

  return stayDurationData.value.bds_list.map((bds: BusinessDistrict) => {
    const station = bds.station_list[0] || {};
    return {
      bds_id: bds.bds_id,
      name: station.station_name || bds.bds_name,
      position: {
        lat: station.station_latitude || 0,
        lng: station.station_longitude || 0
      },
      icon: subwayIcon, // 这里替换为实际的图标路径
      popupData: {
        station_name: station?.station_name || "", // 明确传递站点名
        local_visitor_count: formatToWan(station?.local_visitor_count || 0),
        non_local_visitor_count: formatToWan(station?.non_local_visitor_count || 0)
      }
    };
  });
});

// 幻灯片切换逻辑
useSlideHook((slide) => {
  if (slide === SLIDE_COMPONENTS_INDEX["registeredResidenceDistribution"]) {
    isShowDataPanel.value = true;
    initChartData();

    nextTick(() => {
      if (chartData.value.length && chartRefDuringHoliday.value) {
        chartRefDuringHoliday.value.initChart();
      }
    });
  } else {
    isShowDataPanel.value = false;
    if (chartRefDuringHoliday.value?.getChartInstance()) {
      chartRefDuringHoliday.value.getChartInstance().dispose();
    }
  }
});

// // 监听数据变化，更新图表
// watch(
//   () => processedData.value,
//   () => {
//     if (isShowDataPanel.value && chartRefDuringHoliday.value) {
//       nextTick(() => {
//         chartRefDuringHoliday.value.initChart();
//       });
//     }
//   },
//   { deep: true }
// );

// 组件挂载时初始化数据
onMounted(() => {});

// 地图加载回调
const handleMapLoad = (value: boolean) => {
  loaded.value = value;
  slideStore.setLoadMaps("registeredResidenceDistribution");
};

// 暴露方法
defineExpose({
  name: "registeredResidenceDistribution",
  preloadMap: () => {
    if (loaded.value) {
      slideStore.setLoadMaps("registeredResidenceDistribution");
    } else {
      setTimeout(() => {
        mapRefForDurationStayLocalGuests.value?.initMap();
      }, 0);
    }
  }
});
</script>

<style scoped lang="scss">
.duration-stay-container {
  position: relative;
  width: 100%;
  height: 100%;
  background-color: #000;
}

.map-section {
  width: 100%;
  height: 100%;
}

.data-panel {
  position: absolute;
  top: 30%;
  right: 50px;
  transform: translateY(-30%);
  z-index: 20;
  pointer-events: none;

  .chart-content {
    padding: 20px;
    pointer-events: auto;
  }
}

.data-panel-title {
  font-size: 22px;
  font-weight: 700;
  color: #b1fff2;
  margin: 0 0 30px 0;
  line-height: 22px;
  pointer-events: auto;
}

.chart-section {
  padding: 0 10px;
  .chart-section-title {
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 0 0 10px 0;

    .chart-title-icon {
      width: 18px;
      height: 22px;
    }

    .chart-title {
      font-size: 16px;
      font-weight: 700;
      color: #f7fffe;
      line-height: 1;
      text-shadow: 0px 4px 6px #4d429e;
    }
  }

  .chart-desc {
    font-size: 12px;
    color: #e6e6e6;
    margin: 0 0 20px 0;
    line-height: 18px;
    word-break: break-all;
    text-align: justify;
    text-shadow: 0px 4px 6px #4d429e;
  }

  .no-data {
    color: rgba(255, 255, 255, 0.7);
    text-align: center;
    padding: 60px 0;
    font-size: 14px;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

// 添加覆盖物悬停效果样式
.district-overlay {
  &:hover {
    transform: translateY(-4px) scale(1.1);
    box-shadow:
      0 12px 32px rgba(0, 0, 0, 0.7),
      0 0 0 2px rgba(255, 255, 255, 0.4),
      0 0 40px rgba(147, 244, 255, 0.5);

    .overlay-icon {
      transform: scale(1.15) rotate(8deg);
      box-shadow: 0 0 24px rgba(147, 244, 255, 0.9);

      > div > div {
        opacity: 0.9;
        transform: scale(1.3);
      }
    }

    .overlay-text {
      text-shadow:
        0 4px 8px rgba(0, 0, 0, 1),
        0 0 15px rgba(147, 244, 255, 0.6);
      transform: translateX(3px);
    }
  }

  .overlay-icon {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);

    > div {
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);

      > div {
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      }
    }
  }

  .overlay-text {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  }
}

.retry-btn {
  background: #93f4ff;
  color: #000;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  margin-top: 15px;
  transition: all 0.3s ease;

  &:hover {
    background: #c3fff6;
  }
}
</style>
