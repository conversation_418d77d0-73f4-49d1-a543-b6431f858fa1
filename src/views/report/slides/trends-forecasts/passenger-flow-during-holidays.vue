<template>
  <layout-main>
    <div class="content-container full flex justify-center relative" @dblclick="handleClick">
      <div class="full bg-img"></div>
      
      <!-- 图表内容区域 -->
      <div class="chart-container flex justify-center">
        <BubbleChart
          ref="chartRefDuringHoliday"
          :title="chartTitle"
          :subtitle="chartSubtitle"
          :data="chartData"
          :categories="categories"
          :colors="colors"
          width="90%"
          height="90%"
          theme="dark"
          :iconEnum="DayIconEnum"
        />
      </div>
    </div>
  </layout-main>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue';
import BubbleChart from './components/bubble-chart.vue';
import { DayIconEnum } from './components/icon-uri-enum';
import { SLIDE_COMPONENTS_INDEX } from "@/views/report/config";
import { useSlideHook } from "../../hooks/useSlideHook";
import chartDataJson from "@/data/visitor-behav-analysis/节假日-周末-工作日客流分析.json";

const chartRefDuringHoliday = ref<any>(null);
useSlideHook((slide) => {
  if (slide === SLIDE_COMPONENTS_INDEX["passengerFlowDuringHolidays"]) {
    chartRefDuringHoliday.value?.initChart();
  } else {
    chartRefDuringHoliday.value?.getChartInstance()?.dispose();
  }
});

const chartData = ref<any>(null);

// 图表标题和副标题
const chartTitle = ref('商圈节假日日客流');
const chartSubtitle = ref('');

// 分类和颜色
const categories = ref(['节假日', '周末', '工作日']);
const colors = ref(['#3EECFF', '#B699FF', '#FFFD89']);

// 初始化图表数据
const initChartData = () => {
  // 更新副标题
  if (chartDataJson.result) {
    // 使用第一个商业区的结论作为副标题
    chartSubtitle.value = chartDataJson.result.page_conclusion || "";
  }
  
  // 构建气泡图表数据
  const bubbleData = buildBubbleChartData(chartDataJson.result);
  chartData.value = bubbleData;
};

// 根据JSON数据构建气泡图表数据
const buildBubbleChartData = (data: any) => {
  if (!data || !data.bds_list) {
    return [];
  }
  
  const bubbleData: any[] = [];
  
  data.bds_list.forEach((district: any) => {
    // 节假日数据
    bubbleData.push({
      name: district.bds_name,
      value: district.holiday_average_flow,
      category: '节假日'
    });
    
    // 周末数据
    bubbleData.push({
      name: district.bds_name,
      value: district.weekday_average_flow,
      category: '周末'
    });
    
    // 工作日数据
    bubbleData.push({
      name: district.bds_name,
      value: district.workday_average_flow,
      category: '工作日'
    });
  });
  
  return bubbleData;
};

const handleClick = () => {
  document.documentElement.requestFullscreen();
};

// 组件挂载时初始化数据
onMounted(() => {
  initChartData();
});
</script>

<style lang="scss" scoped>
.content-container {
  padding-top: 60px;
}
.bg-img {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('@/assets/visitor-group-bg.png') no-repeat;
  background-size: 100% 100%;
  background-position: center;
  filter: brightness(0.4);
}
.chart-container {
  width: 100%;
  height: calc(100% - 140px);
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
