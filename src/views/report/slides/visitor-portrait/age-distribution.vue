<template>
  <layout-main>
    <div class="duration-stay-container">
      <bds-map
        ref="durationStayLocalGuestsMap"
        :showBubble="true"
        show-name
        :bubble-data="bubbleData"
        @load="handleMapLoad"
      />

      <!-- 右侧数据面板 -->
      <div class="data-panel z-2000" v-if="isShowDataPanel && stayDurationData">
        <div class="data-panel-title">客流年龄分布分析</div>
        <card-slot card-type="cyan" :width="470" :height="465">
          <div class="chart-content">
            <!-- 图表组件：增加loading/无数据兜底 -->
            <div class="chart-section">
              <div class="chart-section-title">
                <img src="@/assets/icon.png" alt="chart-title-icon" class="chart-title-icon" />
                <h4 class="chart-title">{{ chartTitle }}</h4>
              </div>
              <p class="chart-desc">
                {{ currentDistrictData?.bds_conclusion || stayDurationData.page_conclusion || "" }}
              </p>
              <!-- 商圈选择器：传递完整商圈列表，确保选项与数据匹配 -->
              <district-selector
                :district-list="districtNameList"
                :default-active="activeDistrict"
                @district-change="handleDistrictChange"
                style="margin-bottom: 0"
              />
              <!-- 无数据兜底 -->
              <div v-if="!currentChartData.length" class="no-data">暂无该商圈的年龄分布数据</div>
              <stay-duration-chart
                v-else
                :chart-data="currentChartData"
                :district-name="activeDistrict"
                :key="activeDistrict"
              />
            </div>
          </div>
        </card-slot>
      </div>
    </div>
  </layout-main>
</template>

<script setup lang="ts">
import CardSlot from "../../components/card-slot.vue";
import DistrictSelector from "../../components/district-selector.vue";
import StayDurationChart from "./portrait-components/stay-duration-chart.vue";
import { SLIDE_COMPONENTS_INDEX } from "@/views/report/config";
import { useSlideHook } from "../../hooks/useSlideHook";
import chartDataJson from "@/data/visitor-user-profile/客流年龄分布分析.json";
import { toWan } from "@/utils";
import { useSlideStore } from "@/store/modules/slide";
import { BubbleData } from "@/views/report/components/bds-map.vue";

// 类型定义
interface BusinessDistrict {
  bds_id: string;
  bds_name: string;
  bds_conclusion: string;
  visit_flow: number;
  sec_list: StayDuration[];
  bds_latitude: number;
  bds_longitude: number;
}

interface StayDuration {
  sec_name: string;
  sec_value: number;
  sec_percentage: number;
}

// 状态管理与ref定义
const slideStore = useSlideStore();
const durationStayLocalGuestsMap = ref<any>(null);
const isShowDataPanel = ref(false);
const activeDistrict = ref("文化博览中心");
const stayDurationData = ref<any>(null);
const bubbleData = ref<BubbleData[]>([]);
const loaded = ref(false);
const chartTitle = ref("年龄阶段占比");

// 1. 生成完整的商圈名称列表（供选择器使用）
const districtNameList = computed(() => {
  if (!stayDurationData.value?.bds_list) return [];
  // 提取所有商圈名称，确保与选择器选项完全匹配
  return stayDurationData.value.bds_list.map((district) => district.bds_name);
});

// 2. 商圈数据映射（按名称匹配）
const districtData = computed(() => {
  if (!stayDurationData.value?.bds_list) return {};
  return stayDurationData.value.bds_list.reduce(
    (acc: Record<string, BusinessDistrict>, district) => {
      acc[district.bds_name] = district; // 以商圈名称为key，确保切换时能精准匹配
      return acc;
    },
    {}
  );
});

// 3. 当前选中的商圈数据（带空值兜底）
const currentDistrictData = computed(() => {
  return districtData.value[activeDistrict.value] || null;
});

// 4. 图表数据（处理百分比、空值兜底）
const currentChartData = computed(() => {
  if (!currentDistrictData.value?.sec_list) return [];

  // 处理数据：百分比转整数，过滤无效数据
  return currentDistrictData.value.sec_list
    .filter((item) => item.sec_percentage !== undefined && item.sec_value !== undefined) // 过滤无效数据
    .map((item) => ({
      name: item.sec_name, // 年龄阶段（如25以下、25-34）
      value: Math.round(item.sec_percentage * 100), // 百分比转整数（如0.19→19）
      count: item.sec_value, // 原始人数
      percentage: item.sec_percentage // 保留原始百分比（可选，供图表hover显示）
    }));
});

// 幻灯片切换逻辑
useSlideHook((slide) => {
  if (slide === SLIDE_COMPONENTS_INDEX["ageDistribution"]) {
    isShowDataPanel.value = true;
    initStayDurationData(); // 组件挂载时初始化数据
  } else {
    isShowDataPanel.value = false;
  }
});

// 5. 处理商圈切换（核心修复）
const handleDistrictChange = (district: string) => {
  // 验证切换的商圈是否存在（避免无效值）
  if (!districtNameList.value.includes(district)) return;

  // 更新选中的商圈（触发后续computed数据更新）
  activeDistrict.value = district;
};

// 6. 生成地图气泡数据
const getBubbleData = (bdsList: BusinessDistrict[]) => {
  if (!bdsList.length) return [];
  // 按访客量降序排序，设置气泡大小
  const sortedList = [...bdsList].sort((a, b) => b.visit_flow - a.visit_flow);

  return sortedList.map((item, index) => ({
    bds_id: item.bds_id,
    value: toWan(item.visit_flow), // 访客量转“万”单位（如176891→17.7万）
    size: index === 0 ? 100 : index === 1 ? 90 : index === 2 ? 80 : 60, // 按排名设置大小
    position: {
      lat: item.bds_latitude,
      lng: item.bds_longitude
    }
  }));
};

// 7. 初始化数据
const initStayDurationData = () => {
  stayDurationData.value = chartDataJson.result;
  const { bds_list = [] } = stayDurationData.value;

  // 初始化气泡数据
  bubbleData.value = getBubbleData(bds_list);

  // 初始化选中的商圈（若默认商圈不存在，选中第一个）
  if (bds_list.length && !districtNameList.value.includes(activeDistrict.value)) {
    activeDistrict.value = bds_list[0].bds_name;
  }
};

// 8. 生命周期与地图加载
onMounted(() => {});

const handleMapLoad = (value: boolean) => {
  loaded.value = value;
  slideStore.setLoadMaps("ageDistribution");
};

// 暴露组件方法
defineExpose({
  name: "ageDistribution",
  preloadMap: () => {
    if (loaded.value) {
      slideStore.setLoadMaps("ageDistribution");
    } else {
      setTimeout(() => {
        durationStayLocalGuestsMap.value?.initMap();
      }, 0);
    }
  }
});
</script>

<style scoped lang="scss">
.duration-stay-container {
  position: relative;
  width: 100%;
  height: 100%;
  background-color: #000;
}

.data-panel {
  position: absolute;
  top: 30%;
  right: 50px;
  transform: translateY(-30%);
  z-index: 20;
  pointer-events: none;

  .chart-content {
    padding: 20px;
    pointer-events: auto;
  }
}

.data-panel-title {
  font-size: 22px;
  font-weight: 700;
  color: #b1fff2;
  margin: 0 0 30px 0;
  line-height: 22px;
  pointer-events: auto;
}

.chart-section {
  padding: 0 10px;

  .chart-section-title {
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 0 0 10px 0;

    .chart-title-icon {
      width: 18px;
      height: 22px;
    }

    .chart-title {
      font-size: 16px;
      font-weight: 700;
      color: #f7fffe;
      line-height: 1;
      text-shadow: 0px 4px 6px #4d429e;
    }
  }

  .chart-desc {
    font-size: 12px;
    color: #e6e6e6;
    margin: 0 0 20px 0;
    line-height: 18px;
    word-break: break-all;
    text-align: justify;
    text-shadow: 0px 4px 6px #4d429e;
  }

  // 无数据样式
  .no-data {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.7);
    text-align: center;
    padding: 40px 0;
    line-height: 1.5;
  }
}

// 覆盖物悬停效果
.district-overlay {
  &:hover {
    transform: translateY(-4px) scale(1.1);
    box-shadow:
      0 12px 32px rgba(0, 0, 0, 0.7),
      0 0 0 2px rgba(255, 255, 255, 0.4),
      0 0 40px rgba(147, 244, 255, 0.5);

    .overlay-icon {
      transform: scale(1.15) rotate(8deg);
      box-shadow: 0 0 24px rgba(147, 244, 255, 0.9);

      > div > div {
        opacity: 0.9;
        transform: scale(1.3);
      }
    }

    .overlay-text {
      text-shadow:
        0 4px 8px rgba(0, 0, 0, 1),
        0 0 15px rgba(147, 244, 255, 0.6);
      transform: translateX(3px);
    }
  }

  .overlay-icon {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);

    > div {
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);

      > div {
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      }
    }
  }

  .overlay-text {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  }
}

.retry-btn {
  background: #93f4ff;
  color: #000;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  margin-top: 15px;
  transition: all 0.3s ease;

  &:hover {
    background: #c3fff6;
  }
}
</style>
