<template>
  <layout-main>
    <div class="duration-stay-container">
      <bds-map
        ref="mapRefForDurationStayLocalGuests"
        :showBubble="true"
        show-name
        :bubble-data="bubbleData"
        @load="handleMapLoad"
      />

      <!-- 右侧数据面板 -->
      <div class="data-panel" v-if="isShowDataPanel">
        <div class="data-panel-title">客流性别分布分析</div>
        <card-slot card-type="purple" :width="525" :height="600">
          <div class="chart-content">
            <div class="chart-section">
              <div class="chart-section-title">
                <img src="@/assets/icon.png" alt="chart-title-icon" class="chart-title-icon" />
                <h4 class="chart-title">不同性别占比</h4>
              </div>
              <p class="chart-desc">
                {{ stayDurationData?.page_conclusion || "" }}
              </p>
              <echartsBarChart
                ref="echartsChart"
                :x-axis-data="processedData.xAxisData"
                :profit-data="processedData.consumeData"
                :expenditure-data="processedData.visitData"
              />
            </div>
          </div>
        </card-slot>
      </div>
    </div>
  </layout-main>
</template>

<script setup lang="ts">
import CardSlot from "@/views/report/components/card-slot.vue";
import echartsBarChart from "@/views/report/components/barChart.vue";
import { SLIDE_COMPONENTS_INDEX } from "@/views/report/config";
import { useSlideHook } from "../../hooks/useSlideHook";
import chartDataJson from "@/data/visitor-user-profile/客流性别分布分析.json";
import { toWan } from "@/utils";
import { useSlideStore } from "@/store/modules/slide";
import { BubbleData } from "@/views/report/components/bds-map.vue";

// 类型定义
interface BusinessDistrict {
  bds_id: string;
  bds_name: string;
  bds_conclusion: string;
  visit_flow: number;
  sec_list: StayDuration[];
  bds_latitude: number;
  bds_longitude: number;
}

interface StayDuration {
  sec_name: string;
  sec_value: number;
  sec_percentage: number;
}

// 状态管理与ref定义
const slideStore = useSlideStore();
const mapRefForDurationStayLocalGuests = ref<any>(null);
const isShowDataPanel = ref(false);
const echartsChart = ref<any>(null);
const stayDurationData = ref<any>(null);
const loaded = ref(false);

// 处理后的数据（初始化空数组，避免ECharts报错）
const processedData = ref({
  xAxisData: [] as string[],
  consumeData: [] as number[], // 女性数据（sec_name: "女"）
  visitData: [] as number[], // 男性数据（sec_name: "男"）
  mapMarkers: [] as any[],
  bottomOverlays: [] as any[]
});
const bubbleData = ref<BubbleData[]>([]);

/**
 * 初始化数据（核心修复逻辑）
 */
const initStayDurationData = () => {
  // 赋值原始数据
  stayDurationData.value = chartDataJson.result;
  const { bds_list = [] } = stayDurationData.value;

  // 1. 生成地图气泡数据（按访客量排序）
  bubbleData.value = getBubbleData(bds_list);

  // 2. 按访客量降序排序商圈（修复原排序逻辑错误）
  const sortedBdsList = [...bds_list].sort((a, b) => b.visit_flow - a.visit_flow);

  // 3. 处理X轴数据（商圈名称换行优化）
  processedData.value.xAxisData = sortedBdsList.map((item) => {
    const name = item.bds_name;
    // 特殊处理"文化博览中心"
    if (name === "文化博览中心") return "文化博览\n中心";
    // 名称长度>4时，中间换行
    if (name.length > 4) {
      const mid = Math.ceil(name.length / 2);
      return `${name.substring(0, mid)}\n${name.substring(mid)}`;
    }
    return name;
  });

  // 4. 处理女性数据（consumeData：sec_name="女"的sec_value）
  processedData.value.consumeData = sortedBdsList.map((item) => {
    const femaleData = item.sec_list.find((sec) => sec.sec_name === "女");
    // 若未找到女性数据，默认返回0（避免NaN）
    return femaleData ? femaleData.sec_value : 0;
  });

  // 5. 处理男性数据（visitData：sec_name="男"的sec_value）
  processedData.value.visitData = sortedBdsList.map((item) => {
    const maleData = item.sec_list.find((sec) => sec.sec_name === "男");
    // 若未找到男性数据，默认返回0（避免NaN）
    return maleData ? maleData.sec_value : 0;
  });
};

/**
 * 生成地图气泡数据
 */
const getBubbleData = (bdsList: BusinessDistrict[]): BubbleData[] => {
  // 按访客量降序排序，用于设置气泡大小
  const sortedList = [...bdsList].sort((a, b) => b.visit_flow - a.visit_flow);

  return sortedList.map((item, index) => {
    return {
      bds_id: item.bds_id,
      value: toWan(item.visit_flow), // 访客量转"万"单位
      // 按排名设置气泡大小（前三差异化，后续统一）
      size: index === 0 ? 100 : index === 1 ? 90 : index === 2 ? 80 : 60,
      position: {
        lat: item.bds_latitude,
        lng: item.bds_longitude
      }
    };
  });
};

// 幻灯片切换逻辑
useSlideHook((slide) => {
  if (slide === SLIDE_COMPONENTS_INDEX["genderDistribution"]) {
    isShowDataPanel.value = true;
    initStayDurationData();
  } else {
    isShowDataPanel.value = false;
  }
});

// 组件挂载时初始化数据
onMounted(() => {});

// 地图加载回调
const handleMapLoad = (value: boolean) => {
  loaded.value = value;
  slideStore.setLoadMaps("genderDistribution");
};

// 暴露组件方法
defineExpose({
  name: "genderDistribution",
  preloadMap: () => {
    if (loaded.value) {
      slideStore.setLoadMaps("genderDistribution");
    } else {
      setTimeout(() => {
        mapRefForDurationStayLocalGuests.value?.initMap();
      }, 0);
    }
  }
});
</script>

<style scoped lang="scss">
.duration-stay-container {
  position: relative;
  width: 100%;
  height: 100%;
  background-color: #000;
}

.data-panel {
  position: absolute;
  top: 30%;
  right: 50px;
  transform: translateY(-30%);
  z-index: 20;
  pointer-events: none;

  .chart-content {
    padding: 26px 17px 0 10px;
    box-sizing: border-box;
    pointer-events: auto;
  }
}

.data-panel-title {
  font-size: 22px;
  font-weight: 700;
  color: #b1fff2;
  margin: 0 0 30px 0;
  line-height: 22px;
  pointer-events: auto;
}

.chart-section {
  .chart-section-title {
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 0 0 10px 0;
    padding-left: 16px;
    box-sizing: border-box;

    .chart-title-icon {
      width: 18px;
      height: 22px;
    }

    .chart-title {
      font-family: Verdana;
      font-size: 16px;
      font-weight: bold;
      line-height: 16px;
      letter-spacing: 0em;
      color: #f7fffe;
      text-shadow: 0px 4px 6px #4d429e;
    }
  }

  .chart-desc {
    font-size: 12px;
    margin: 0 0 20px 0;
    line-height: 18px;
    word-break: break-all;
    text-align: justify;
    font-variation-settings: "opsz" auto;
    color: rgba(255, 255, 255, 0.8);
    text-shadow: 0px 4px 6px #4d429e;
    padding-left: 16px;
    padding-right: 50px;
    box-sizing: border-box;
  }
}

.retry-btn {
  background: #93f4ff;
  color: #000;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  margin-top: 15px;
  transition: all 0.3s ease;

  &:hover {
    background: #c3fff6;
  }
}
</style>
