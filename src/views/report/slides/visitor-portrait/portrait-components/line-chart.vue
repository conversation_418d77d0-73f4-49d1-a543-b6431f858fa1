<template>
  <!-- 线图容器 -->
  <div class="line-chart-container">
    <!-- 图表头部区域 -->
    <div class="chart-header">
      <!-- 图表标题 -->
      <h2 class="chart-title">{{ title }}</h2>
      <!-- 图表副标题 -->
      <p v-if="subtitle" class="chart-subtitle">{{ subtitle }}</p>
    </div>
    <!-- 添加 slot 区域 -->
    <slot name="chart-menu"></slot>
    <!-- 图表内容区域 -->
    <div ref="chartRef" class="chart-content"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from "vue";
import * as echarts from "echarts";

// 图表数据项接口定义
interface ChartDataItem {
  name: string; // 数据系列名称
  data: number[]; // 数据数组
  color?: string; // 线条颜色（可选）
  smooth?: boolean; // 是否平滑曲线（可选）
  areaStyle?: boolean; // 是否显示区域填充（可选）
  isShowSymbol?: boolean; // 是否显示标记点（可选）
  symbolIconType?: string; // 标记点图标类型（可选）
}

// 组件属性接口定义
interface Props {
  title?: string; // 图表标题
  subtitle?: string; // 图表副标题
  xAxisData?: string[]; // X轴数据
  series?: ChartDataItem[]; // 数据系列
  option?: echarts.EChartsOption; // 自定义ECharts配置
  width?: string | number; // 图表宽度
  height?: string | number; // 图表高度
  theme?: "dark" | "light"; // 图表主题
  isTenThousandUnits?: boolean; // 是否显示万单位（可选）
}

// 定义组件属性，设置默认值
const props = withDefaults(defineProps<Props>(), {
  title: "图表标题",
  subtitle: "",
  xAxisData: () => [],
  series: () => [],
  width: "100%",
  height: "400px",
  theme: "dark",
  isTenThousandUnits: true
});

// 图表DOM引用
const chartRef = ref<HTMLElement>();
// 图表实例
let chartInstance: echarts.ECharts | null = null;

// 获取默认的图表配置
const getDefaultOption = (): echarts.EChartsOption => {
  return {
    // 背景色设置为透明
    backgroundColor: "transparent",
    // 标题配置（隐藏默认标题）
    title: {
      show: false
    },
    // 提示框配置
    tooltip: {
      trigger: "axis",
      backgroundColor: "rgba(31, 26, 34, 0.9)", // 背景色
      borderColor: "rgba(31, 26, 34, 0.9)", // 边框色
      borderWidth: 1, // 边框宽度
      textStyle: {
        color: "#fff", // 文字颜色
        fontSize: 12 // 字体大小
      },
      // 坐标轴指示器配置
      axisPointer: {
        type: "line", // 指示器类型为线条
        lineStyle: {
          color: "rgba(226, 246, 254, 1)", // 指示器颜色
          width: 1 // 指示器宽度
        }
      },
      // 自定义提示框内容格式化
      formatter: (params: any) => {
        if (!params || params.length === 0) return "";

        // 构建HTML格式的提示框内容
        const chartHtml = `
          <div>
            <div style="font-size: 12px; font-weight: 400; color: #fff;">${params[0].name}</div>
            ${params
              .map((param: any, index: number) => {
                // 设置颜色，第一个系列为紫色，其他为青色
                const color = param.color || (index === 0 ? "#A967FF" : "#1FFFFF");
                // 数值格式化：超过10000显示为万
                const value = props.isTenThousandUnits
                  ? param.value / 10000 + "万"
                  : param.value.toString();

                return `
                <div style="display: flex; justify-content: space-between; align-items: center; gap: 10px;">
                  <div style="width: 20px; height: 4px; background-color: ${color};"></div>
                  <div style="font-size: 12px; font-weight: 400; color: #fff;">${param.seriesName}</div>
                  <div style="font-size: 12px; font-weight: 400; color: ${color};">${value}</div>
                </div>
              `;
              })
              .join("")}
          </div>
        `;
        return chartHtml;
      }
    },
    // 图例配置
    legend: {
      top: 10, // 距离顶部距离
      right: 20, // 距离右侧距离
      textStyle: {
        color: "#fff", // 文字颜色
        fontSize: 12 // 字体大小
      },
      itemWidth: 12, // 图例标记宽度
      itemHeight: 12 // 图例标记高度
    },
    // 图表网格配置
    grid: {
      top: 60, // 距离顶部距离
      left: 0, // 距离左侧距离
      right: 10, // 距离右侧距离
      bottom: 30, // 距离底部距离
      containLabel: true // 包含坐标轴标签
    },
    // X轴配置
    xAxis: {
      type: "category", // 类型为类目轴
      data: props.xAxisData, // X轴数据
      axisLine: {
        show: true, // 显示坐标轴线
        lineStyle: {
          color: "#8490A9" // 轴线颜色
        }
      },
      axisTick: {
        show: false // 隐藏刻度线
      },
      axisLabel: {
        color: "rgba(255, 255, 255, 0.7)", // 标签颜色
        fontSize: 12 // 字体大小
      }
    },
    // Y轴配置
    yAxis: {
      type: "value", // 类型为数值轴
      axisLine: {
        show: true, // 显示坐标轴线
        lineStyle: {
          color: "#8490A9" // 轴线颜色
        }
      },
      axisTick: {
        show: false // 隐藏刻度线
      },
      splitLine: {
        show: false // 隐藏分割线
      },
      axisLabel: {
        color: "rgba(255, 255, 255, 0.7)", // 标签颜色
        fontSize: 12, // 字体大小
        // 标签格式化：超过10000显示为万
        formatter: (value: number) => {
          if (props.isTenThousandUnits) {
            return value > 0 ? value / 10000 + "万" : "0";
          }
          return value.toString();
        }
      }
    },
    // 数据系列配置
    series: props.series.map((item) => ({
      name: item.name, // 系列名称
      type: "line", // 图表类型为线图
      data: item.data, // 数据
      smooth: item.smooth ?? true, // 是否平滑曲线，默认true
      showSymbol: item.isShowSymbol, // 是否显示标记点
      symbol: item.symbolIconType, // 标记点图标类型
      symbolSize: 12, // 标记点大小
      lineStyle: {
        width: 2, // 线条宽度
        color: item.color // 线条颜色
      },
      itemStyle: {
        color: item.color // 标记点颜色
      },
      // 区域填充样式（可选）
      areaStyle: item.areaStyle
        ? {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: item.color + "40" }, // 渐变起始色
              { offset: 1, color: item.color + "10" } // 渐变结束色
            ])
          }
        : undefined
    }))
  };
};

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return;

  // 创建ECharts实例
  chartInstance = echarts.init(chartRef.value, props.theme, { renderer: "svg" });

  // 合并用户配置和默认配置
  const finalOption = props.option
    ? echarts.util.merge(getDefaultOption(), props.option)
    : getDefaultOption();

  // 设置图表配置
  chartInstance.setOption(finalOption);
};

// 更新图表
const updateChart = () => {
  if (!chartInstance) return;

  // 重新合并配置
  const finalOption = props.option
    ? echarts.util.merge(getDefaultOption(), props.option)
    : getDefaultOption();

  // 更新图表配置
  chartInstance.setOption(finalOption);
};

// 监听数据变化，自动更新图表
watch(
  () => [props.series, props.xAxisData, props.option],
  () => {
    nextTick(() => {
      updateChart();
    });
  },
  { deep: true } // 深度监听对象变化
);

// 组件挂载时初始化
onMounted(() => {});

// 组件卸载时清理资源
onUnmounted(() => {
  // 销毁图表实例
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }
});

// 暴露方法给父组件使用
defineExpose({
  // 获取图表实例
  getChartInstance: () => chartInstance,
  // 重新调整图表大小
  resize: () => chartInstance?.resize(),
  // 设置图表配置
  setOption: (option: echarts.EChartsOption) => chartInstance?.setOption(option),
  // 初始化图表
  initChart: () => initChart()
});
</script>

<style lang="scss" scoped>
.line-chart-container {
  width: v-bind(width);
  height: v-bind(height);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;

  .chart-header {
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .chart-title {
      color: #b1fff2;
      font-size: 22px;
      font-weight: 700;
      margin: 0 0 12px 0;
      line-height: 1.2;
    }

    .chart-subtitle {
      max-width: 710px;
      height: 55px;
      color: #ffffff;
      font-size: 14px;
      font-weight: 400;
      white-space: normal;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      margin: 0;
      line-height: 18px;
    }
  }

  .chart-content {
    width: 100%;
    height: calc(100% - 120px);
  }
}

.tooltip-box {
  display: flex;
  flex-direction: column;
  gap: 10px;

  .itme-type {
    color: #fff;
    font-size: 12px;
    font-weight: 400;
  }

  .item-data {
    display: flex;
    align-items: center;
    gap: 10px;

    /* 提示框图标 */
    .item-icon {
      width: 20px;
      height: 5px;
    }
    /* 紫色图标 */
    .purple {
      background-color: #a967ff;
    }
    /* 绿色图标 */
    .green {
      background-color: #1fffff;
    }

    /* 提示框项目名称 */
    .tooltip-item-name {
      color: #fff;
      font-size: 12px;
      font-weight: 400;
    }
    /* 提示框项目数值 */
    .tooltip-item-value {
      color: #fff;
      font-size: 12px;
      font-weight: 400;
    }
  }
}
</style>
