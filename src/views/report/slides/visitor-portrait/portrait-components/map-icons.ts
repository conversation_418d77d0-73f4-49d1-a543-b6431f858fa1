/**
 * 地图标记点图标管理
 */

// 图标路径映射
export const markerIcons = {
  // 自定义图标（需要将图标文件放在 src/assets 目录下）
  cyan: new URL("@/assets/cyan-marker-icon.png", import.meta.url).href,
  default: new URL("@/assets/default-marker-icon.png", import.meta.url).href,
  yellow: new URL("@/assets/yellow-marker-icon.png", import.meta.url).href,
  purple: new URL("@/assets/purple-marker-icon.png", import.meta.url).href
};

// 商圈建筑图标映射
export const bdsIcons: { [key: string]: string } = {
  cyan: new URL("@/assets/cyan_build_icon.png", import.meta.url).href,
  green: new URL("@/assets/green_build_icon.png", import.meta.url).href,
  yellow: new URL("@/assets/yellow_build_icon.png", import.meta.url).href,
  purple: new URL("@/assets/purple_build_icon.png", import.meta.url).href
};

/**
 * 获取图标路径
 * @param iconType 图标类型
 * @returns 图标URL路径
 */
export function getMarkerIcon(iconType: string): string {
  return markerIcons[iconType as keyof typeof markerIcons] || markerIcons.default;
}

/**
 * 图标类型枚举
 */
export enum MarkerIconType {
  DEFAULT = "default",
  GREEN = "green",
  YELLOW = "yellow",
  PURPLE = "purple"
}
