<template>
  <layout-main>
    <div class="w-full h-full">
      <div ref="businessOverviewMap" class="c-common-map w-full h-full"></div>
    </div>
  </layout-main>
</template>

<script lang="ts" setup>
import districtHomeData from "@/data/business-district-overview/商圈首页.json";
import { useSlideStore } from "@/store/modules/slide";
import { BdsOverviewData } from "./index";
import { useBdsTitleHook } from "@/views/report/hooks/useBdsTitleHook";
import { MAP_SLIDE_POSE_TYPE } from "@/views/report/type";
import { cloneDeep } from "lodash-es";

const slideStore = useSlideStore();
const businessOverviewMap = ref(null);
const loaded = ref(false); // 地图是否加载完成
const isLoading = ref(false); // 地图是否正在加载
const TMap = (window as any).TMap; // 地图类
let mapInstance: any = null;

const initMap = async () => {
  if (loaded.value) return;

  try {
    console.log("地图加载中...", businessOverviewMap.value, TMap);
    // 加载地图实例
    mapInstance = await new TMap.Map(businessOverviewMap.value, {
      zoom: 14.5, // 设置地图缩放级别
      viewMode: "3D", // 设置地图模式
      pitch: 45, // 设置俯仰角
      rotation: -20, // 设置地图旋转角度
      mapStyleId: "style1",
      zIndex: 10,
      minZoom: 13,
      maxZoom: 18,
      center: new TMap.LatLng(31.31331, 120.69778), // 设置中心点

      baseMap: {
        type: "vector",
        features: ["base", "building3d", "label"],
        buildingRange: [14.5, 23] // 设置建筑物楼块的显示级别，目前设置成了所支持的最大范围[14.5, 25]
      }
    });
    if (mapInstance) {
      mapInstance.removeControl(TMap.constants.DEFAULT_CONTROL_ID.ZOOM); // 从地图容器移出 缩放控件,
      mapInstance.removeControl(TMap.constants.DEFAULT_CONTROL_ID.SCALE); // 从地图容器移出 比例尺控件,
      mapInstance.removeControl(TMap.constants.DEFAULT_CONTROL_ID.ROTATION); // 从地图容器移出 旋转控件,
    }
    mapInstance.on("tilesloaded", async () => {
      console.log("地图加载完成！");
      if (!loaded.value) {
        loaded.value = true;
        slideStore.setLoadMaps("businessOverview");
        updateMapData(mapInstance);
      }
    });
  } catch (error) {
    console.error("地图加载失败:", error);
  } finally {
    isLoading.value = false;
  }
};

const updateMapData = (mapInstance: any) => {
  const { bds_list = [] } = districtHomeData.result;
  // 排序，按客流
  const bdsMapData = (bds_list as BdsOverviewData[]).sort(
    (a, b) => b.average_day_flow - a.average_day_flow
  );
  // 绘制商圈数据弹窗
  createBdsWindows(bdsMapData, mapInstance);
  // 绘制商圈围栏
  createMultiPolygon(bdsMapData, mapInstance);
  // 绘制商圈名称
  createBdsTitle(bdsMapData, mapInstance);
};

// 创建商圈围栏
const createMultiPolygon = (data: BdsOverviewData[], mapInstance: any) => {
  const multiPolygon = new TMap.MultiPolygon({
    id: "multiPolygon",
    map: mapInstance,
    styles: {
      topOne: new TMap.PolygonStyle({
        color: "rgba(80, 244, 245, 0.45)", // 面填充色
        showBorder: true, // 是否显示拔起面的边线
        borderColor: "#C3FFF6", // 边线颜色
        borderDashArray: [3, 3], // 边线样式
        borderWidth: 2 // 边线宽度
      }),
      topTwo: new TMap.PolygonStyle({
        color: "rgba(242, 251, 180, 0.45)", // 面填充色
        showBorder: true, // 是否显示拔起面的边线
        borderColor: "#E8FF38", // 边线颜色
        borderDashArray: [3, 3], // 边线样式
        borderWidth: 2 // 边线宽度
      }),
      topThree: new TMap.PolygonStyle({
        color: "rgba(249, 183, 255, 0.3)", // 面填充色
        showBorder: true, // 是否显示拔起面的边线
        borderColor: "#B647FF", // 边线颜色
        borderDashArray: [3, 3], // 边线样式
        borderWidth: 2 // 边线宽度
      }),
      normalPloygon: new TMap.PolygonStyle({
        color: "rgba(184, 255, 199, 0.11)", // 面填充色
        showBorder: true, // 是否显示拔起面的边线
        borderColor: "#B8FFC7", // 边线颜色
        borderDashArray: [3, 3], // 边线样式
        borderWidth: 2 // 边线宽度
      })
    }
  });

  const polygons = data.map((item: BdsOverviewData, index: number) => {
    const path = item.boundary
      .split(";")
      .map((poi: any) => {
        const latLng = poi.split(",");
        return poi
          ? {
              lat: Number(latLng[1]),
              lng: Number(latLng[0]),
              height: 0
            }
          : null;
      })
      .filter((item: any) => item);

    // 判断当前商圈排名

    const styleId: { [key: number]: string } = {
      0: "topOne",
      1: "topTwo",
      2: "topThree"
    };

    return {
      id: item.bds_id,
      styleId: styleId[index] ?? "normalPloygon", // 样式id
      paths: path, // 多边形的位置信息
      properties: {
        // 多边形的属性数据
        title: "polygon"
      }
    };
  });
  multiPolygon.setGeometries(polygons);

  // 高亮选中图层
  const highlightLayer = new TMap.MultiPolyline({
    map: mapInstance,
    id: "highlightLayer",
    zIndex: 1,
    styles: {
      highlight: new TMap.PolylineStyle({
        color: "#19FFDC", //填充色
        width: 2, // 线宽
        borderColor: "#fff", //边线颜色
        borderWidth: 1 //边线宽度
      })
    }
  });
  multiPolygon.on("click", (e: any) => {
    // 展示数据
    if (e.geometry) {
      Object.keys(bdsWindows).forEach((key) => {
        if (key === e.geometry.id) {
          bdsWindows[key].open();
        } else {
          bdsWindows[key].close();
        }
      });
    }
  });
  // 高亮处理
  multiPolygon.on("hover", (e: any) => {
    if (e.geometry) {
      let paths = cloneDeep(e.geometry.paths);
      paths.push(paths[0]);
      highlightLayer.setGeometries([
        {
          styleId: "highlight",
          paths: paths
        }
      ]);
    } else {
      highlightLayer.setGeometries([]);
    }
  });
};

const bdsWindows: { [key: string]: any } = {};

// 创建商圈数据弹窗
const createBdsWindows = (data: BdsOverviewData[], mapInstance: any) => {
  data.forEach((item: BdsOverviewData, index: number) => {
    const themeClass: { [key: number]: string } =
      {
        0: "top-one",
        1: "top-two",
        2: "top-three"
      }[index] ?? "";

    const infoContent = `
          <div class="v-bds-info-window ${themeClass} ptb-12 pl-10 pr-4">
              <div class="flex-col fs-12 fs-12 ">
                <div class="flex items-center justify-between">
                    <p>商业面积：</p>
                    <p class="flex-1 text-right "><span class="fs-16 mr-4 font-bold">${item.retail_area || 0}</span><span class="w-20 inline-block text-left ">m<sup>2</sup></span></p>
                </div>
                <div class="flex items-center justify-between  mt-8">
                    <p>商户入驻：</p>
                    <p class="flex-1 text-right"><span class="fs-16  mr-4 font-bold">${item.commercial_tenant_num || 0}</span><span class="w-20 inline-block" text-left>个</span> </p>
                </div>
                <div class="flex items-center justify-between mt-8">
                    <p>商业载体入驻：</p>
                    <p class="flex-1 text-right"><span class="fs-16  mr-4 font-bold">${item.commercial_num || 0}</span><span class="w-20 inline-block text-left ">个</span> </p>
                </div>
            </div>
          </div>
      `;

    const position = new TMap.LatLng(item.bds_center_latitude, item.bds_center_longitude);
    const bdsWindow = new TMap.InfoWindow({
      map: mapInstance,
      id: item.bds_id,
      position,
      enableCustom: true, // 是否自定义窗体
      content: infoContent, // 将 html 传给 content
      zIndex: 10,
      offset: { x: 0, y: -30 } // 以 icon 的 [center bottom] 为原点
    });
    // 默认关闭
    index == 0 ? bdsWindow.open() : bdsWindow.close();

    bdsWindows[item.bds_id] = bdsWindow;
  });

  console.log("bdsWindows", bdsWindows);
};

const createBdsTitle = (data: any, mapInstance: any) => {
  data.forEach((item: any, index: number) => {
    useBdsTitleHook({
      map: mapInstance,
      content: item.bds_name,
      position: new TMap.LatLng(item.bds_center_latitude, item.bds_center_longitude),
      fontSize: 13,
      sort: index
    });
  });
};

onUnmounted(() => {
  console.log("销毁地图实例");
  mapInstance && mapInstance.destroy();
});

defineExpose<MAP_SLIDE_POSE_TYPE>({
  name: "businessOverview",
  preloadMap: () => {
    if (loaded.value) {
      slideStore.setLoadMaps("businessOverview");
    } else {
      setTimeout(() => {
        initMap();
      }, 0);
    }
  }
});
</script>

<style lang="scss">
.v-bds-info-window {
  min-width: 160px;
  background-color: #c3fff6;
  border-radius: 10px;
  position: relative;
  color: #1b3e39;
  user-select: none;
  color: #1b3e39;
  &::before {
    content: "";
    position: absolute;
    bottom: -24px;
    left: 50%;
    transform: translateX(-50%);
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: inherit;
  }

  &::after {
    content: "";
    position: absolute;
    bottom: -9px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 10px solid transparent;
    border-right: 10px solid transparent;
    border-top: 10px solid #c3fff6;
  }

  &.top-one {
    background-color: #50f4f5;
    &::after {
      border-top-color: #50f4f5;
    }
  }
  &.top-two {
    background-color: rgba(242, 251, 180, 1);
    &::after {
      border-top-color: rgba(242, 251, 180, 1);
    }
  }
  &.top-three {
    background-color: #f9b7ff;
    &::after {
      border-top-color: #f9b7ff;
    }
  }
}
</style>
