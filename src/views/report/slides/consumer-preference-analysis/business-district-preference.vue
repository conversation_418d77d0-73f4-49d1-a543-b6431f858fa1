<template>
  <layout-main>
    <div class="duration-stay-container">
      <!-- 地图组件 -->
      <map-component
        ref="mapRefForDurationStayLocalGuests"
        :center="mapCenter"
        :markers="mapMarkers"
        :custom-overlays="bottomOverlays"
        :zoom="14.5"
        :pitch="45"
        :rotation="-20"
        :view-mode="'3D'"
      />

      <!-- 右侧数据面板 title="到访客群行为分析" -->
      <div class="data-panel" v-if="isShowDataPanel">
        <div class="data-panel-title">消费偏好分析</div>
        <card-slot card-type="purple" :width="525" :height="481">
          <div class="chart-content">
            <!-- 图表组件 -->
            <div class="chart-section">
              <div class="chart-section-title">
                <img src="@/assets/icon.png" alt="chart-title-icon" class="chart-title-icon" />
                <h4 class="chart-title">消费偏好分析</h4>
              </div>
              <p class="chart-desc">
                {{ stayDurationData?.result?.page_conclusion }}
              </p>
              <echartsBarChart
                ref="echartsChart"
                :x-axis-data="xAxisData"
                :consume-data="consumeData"
                :visit-data="visitData"
              />
            </div>
          </div>
        </card-slot>
      </div>
    </div>
  </layout-main>
</template>

<script setup lang="ts">
import { nextTick, ref, computed, onMounted } from "vue";
import MapComponent from "@/views/report/components/map-component.vue";
import CardSlot from "@/views/report/components/card-slot.vue";
import echartsBarChart from "@/views/report/components/echartsBarChart.vue";
import { bdsIcons } from "@/views/report/components/map-icons";

import { SLIDE_COMPONENTS_INDEX } from "@/views/report/config";
import { useSlideHook } from "../../hooks/useSlideHook";
import consumer from "@/data/consumer-preference-analysis/消费人群占比.json";

const mapRefForDurationStayLocalGuests = ref<any>(null);
const isShowDataPanel = ref(false);
const xAxisData = ref<string[]>([]);
const consumeData = ref<number[]>([]);
const visitData = ref<number[]>([]);
const echartsChart = ref<any>(null);

useSlideHook(async (slide) => {
  if (slide === SLIDE_COMPONENTS_INDEX["businessDistrictPreference"]) {
    // 确保数据已加载
    if (!stayDurationData.value) {
      await fetchStayDurationData();
    }
    isShowDataPanel.value = true;
    // 等待数据更新和DOM更新后再初始化地图
    nextTick(() => {
      mapRefForDurationStayLocalGuests.value?.initMap();

      // 触发图表初始化
      // 确保图表组件已挂载后再初始化
      setTimeout(() => {
        if (xAxisData.value.length > 0 && echartsChart.value) {
          echartsChart.value.initChart();
        }
      }, 100);
    });
  } else {
    // 切换时销毁地图，清理覆盖物
    mapRefForDurationStayLocalGuests.value?.destroy?.();
    isShowDataPanel.value = false;
    if (echartsChart.value?.myChart()) {
      echartsChart.value.myChart().dispose();
    }
  }
});

// 响应式数据
const stayDurationData = ref<any>(null);

// 地图中心点坐标（苏州中心区域）
const mapCenter = {
  lat: 31.314001,
  lng: 120.69778
};

// 地图标记点
const mapMarkers = computed(() => {
  if (!stayDurationData.value) return [];

  // 计算标记点大小：根据 stay_num 值计算
  const minSize = 40;
  const maxSize = 90;
  const minAverageStay = Math.min(
    ...stayDurationData.value.result.bds_list.map((d: any) => d.consume_count || 0)
  );
  const maxAverageStay = Math.max(
    ...stayDurationData.value.result.bds_list.map((d: any) => d.consume_count || 0)
  );
  // 根据 consume_count 进行降序排序
  const sortedBdsList = stayDurationData.value.result.bds_list.sort(
    (a: any, b: any) => b.consume_count - a.consume_count
  );

  const markers = sortedBdsList.map((district: any, index: number) => {
    // 获取平均停留时长
    const averageStay = district.consume_count;
    const averageStayMultiple = Number(
      (averageStay / (maxAverageStay - minAverageStay || 1)).toFixed(2) // 避免除以0
    );

    // 根据 stay_num 计算 marker 大小
    let markerSize = minSize * averageStayMultiple;
    if (markerSize < minSize) {
      // 线性插值计算大小
      markerSize = minSize;
    } else if (markerSize > maxSize) {
      markerSize = maxSize;
    }
    markerSize = Math.ceil(markerSize);

    // 确定图标颜色
    const colorTypes = ["cyan", "yellow", "purple"];

    const markerObj = {
      id: district.bds_id.toString(),
      position: {
        lat: parseFloat(district.bds_latitude),
        lng: parseFloat(district.bds_longitude)
      },
      averageStayMultiple: averageStayMultiple,
      text: `${averageStay}万`,
      style: {
        size: markerSize, // 根据 stay_num 计算的动态大小
        icon: index < 3 ? colorTypes[index] : "default" // 根据 stay_num 确定的图标颜色
      },
      properties: {
        districtName: district.bds_name // 商圈名称
      }
    };
    return markerObj;
  });

  return markers;
});

// 底部覆盖物数据
const bottomOverlays = computed(() => {
  if (!stayDurationData.value) return [];

  console.log("生成覆盖物数据，数量:", stayDurationData.value.result.bds_list.length);

  // 根据 consume_count 进行降序排序
  const sortedBdsList = stayDurationData.value.result.bds_list.sort(
    (a: any, b: any) => b.consume_count - a.consume_count
  );

  return sortedBdsList.map((district: any, index: number) => {
    // 确定图标颜色
    const colorTypes = ["cyan", "yellow", "purple"];
    const colorType = index < 3 ? colorTypes[index] : "green";

    // 根据颜色类型确定边框和文字颜色
    const getColorByType = (type: string) => {
      switch (type) {
        case "cyan":
          return "#50F4F5"; // 浅蓝色
        case "yellow":
          return "#F4FFA2"; // 金黄色
        case "purple":
          return "#FF99E7"; // 中紫色
        case "green":
          return "#A8FAB7"; // 绿色
        default:
          return "#50F4F5";
      }
    };

    const borderColor = getColorByType(colorType);

    // // 计算覆盖物位置偏移，确保显示在标记点下方
    // const markerSize = Math.min(
    //   90,
    //   Math.max(
    //     40,
    //     40 *
    //       (district.consume_count /
    //         Math.max(
    //           ...stayDurationData.value.result.bds_list.map((d: any) => d.consume_count || 0),
    //           1 // 避免除以0
    //         ))
    //   )
    // );
    // const offsetY = markerSize / 2 + 25; // 标记点高度的一半 + 额外间距
    // const offsetX = (mapMarkers.value[index]?.style?.size || 40) / 2 - 4;

    // 创建覆盖物HTML内容
    const overlayContent = `
      <div class="district-overlay" style="
        display: flex;
        align-items: center;
        gap: 2px;
        position: relative;
        padding: 3px 6px;
        background: rgba(0, 0, 0, 0.48);
        border: 1px solid ${borderColor};
        border-radius: 12px;
        color: ${borderColor};
        font-size: 12px;
        white-space: nowrap;
        z-index: 1;
        width: fit-content;
        user-select: none;
      ">
        <img src="${bdsIcons[colorType]}" 
          alt="chart-title-icon" 
          class="chart-title-icon"
          style="width: 16px; height: 16px;"
        />
        <span class="overlay-text" style="
          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.9);
          letter-spacing: 0.8px;
          position: relative;
          z-index: 1;
        ">${district.bds_name}</span>
      </div>
    `;

    return {
      id: `overlay-${district.bds_id}`,
      position: {
        lat: parseFloat(district.bds_latitude),
        lng: parseFloat(district.bds_longitude)
      },
      content: overlayContent,
      style: {
        borderColor: borderColor,
        color: borderColor
      },
      alignment: "bottom-center" as const
    };
  });
});

// 获取数据的方法
const fetchStayDurationData = async () => {
  try {
    const data = consumer;
    stayDurationData.value = data;

    // 处理数据并更新图表
    if (data.result && data.result.bds_list) {
      xAxisData.value = data.result.bds_list.map((item: any) => {
        if (item.bds_name === "文化博览中心") {
          return "文化博览\n中心";
        }
        // 对于长名称进行换行处理
        if (item.bds_name && item.bds_name.length > 4) {
          const name = item.bds_name;
          const mid = Math.ceil(name.length / 2);
          return name.substring(0, mid) + "\n" + name.substring(mid);
        }
        return item.bds_name;
      });

      consumeData.value = data.result.bds_list.map(
        (item: any) => parseFloat(item.consume_count) || 0
      );

      visitData.value = data.result.bds_list.map(
        (item: any) => parseFloat(item.visit_count) || 100
      );

      // 数据更新后初始化图表
      if (isShowDataPanel.value) {
        nextTick(() => {
          echartsChart.value?.initChart();
        });
      }
    }
  } catch (err) {
    console.error("获取消费偏好分析数据失败:", err);
  }
};

// 监听滚动事件
onMounted(async () => {
  // 预先加载数据
  await fetchStayDurationData();
});
</script>

<style scoped lang="scss">
.duration-stay-container {
  position: relative;
  width: 100%;
  height: 100%;
  background-color: #000;
}

.map-section {
  width: 100%;
  height: 100%;
}

.data-panel {
  position: absolute;
  top: 30%;
  right: 50px;
  transform: translateY(-30%);
  z-index: 20;
  pointer-events: none;

  .chart-content {
    padding: 26px 17px 0 10px;
    box-sizing: border-box;
    pointer-events: auto;
  }
}

.data-panel-title {
  font-size: 22px;
  font-weight: 700;
  color: #b1fff2;
  margin: 0 0 30px 0;
  line-height: 22px;
  pointer-events: auto;
}

.chart-section {
  .chart-section-title {
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 0 0 10px 0;
    padding-left: 16px;
    box-sizing: border-box;

    .chart-title-icon {
      width: 18px;
      height: 22px;
    }

    .chart-title {
      font-family: Verdana;
      font-size: 16px;
      font-weight: bold;
      line-height: 16px;
      letter-spacing: 0em;
      color: #f7fffe;
      text-shadow: 0px 4px 6px #4d429e;
    }
  }

  .chart-desc {
    font-size: 12px;
    color: #e6e6e6;
    margin: 0 0 20px 0;
    line-height: 18px;
    word-break: break-all;
    text-align: justify;
    font-variation-settings: "opsz" auto;
    color: rgba(255, 255, 255, 0.8);
    text-shadow: 0px 4px 6px #4d429e;
    padding-left: 16px;
    padding-right: 50px;
    box-sizing: border-box;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.retry-btn {
  background: #93f4ff;
  color: #000;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  margin-top: 15px;
  transition: all 0.3s ease;

  &:hover {
    background: #c3fff6;
  }
}
</style>
