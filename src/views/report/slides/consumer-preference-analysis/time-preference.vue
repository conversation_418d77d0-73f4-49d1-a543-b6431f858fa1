<template>
  <layout-main>
    <div class="w-full h-full relative">
      <!-- 地图组件 -->
      <bds-map
        ref="mapRefForDurationStayLocalGuests"
        :showBubble="true"
        show-name
        :bubble-data="bubbleData"
        @load="handleMapLoad"
      />

      <!-- 右侧数据面板 -->
      <div class="data-panel" v-if="isShowDataPanel && stayDurationData">
        <div class="data-panel-title">时间偏好分析</div>
        <card-slot card-type="purple" :width="470" :height="465">
          <div class="chart-content">
            <div class="chart-section">
              <div class="chart-section-title">
                <img src="@/assets/icon.png" alt="图表标题图标" class="chart-title-icon" />
                <h4 class="chart-title">{{ chartTitle }}</h4>
              </div>
              <p class="chart-desc">{{ stayDurationData?.page_conclusion || "" }}</p>

              <!-- 商圈选择器：传递完整商圈列表 -->
              <district-selector
                :district-list="districtNameList"
                :default-active="activeDistrict"
                @district-change="handleDistrictChange"
                style="margin-bottom: 0"
              />

              <stay-duration-chart
                :chart-data="chartData"
                :district-name="activeDistrict"
                :key="activeDistrict"
              />
            </div>
          </div>
        </card-slot>
      </div>
    </div>
  </layout-main>
</template>

<script setup lang="ts">
import CardSlot from "../../components/card-slot.vue";
import DistrictSelector from "../../components/district-selector.vue";
import StayDurationChart from "../../components/stay-duration-chart.vue";
import { SLIDE_COMPONENTS_INDEX } from "@/views/report/config";
import { useSlideHook } from "../../hooks/useSlideHook";
import chartDataJson from "@/data/consumer-preference-analysis/时间偏好.json";
import { toWan } from "@/utils";
import { useSlideStore } from "@/store/modules/slide";
import { BubbleData } from "@/views/report/hooks/useBdsMarkerHook";

// 类型定义 - 增强类型安全
interface BusinessDistrict {
  bds_id: string;
  bds_name: string;
  bds_conclusion: string;
  consumer_count: number;
  sec_list: StayDuration[];
  bds_latitude: number;
  bds_longitude: number;
}

interface StayDuration {
  sec_name: string; // 时间段（如"17-22点"）
  sec_value: number; // 原始人数
  sec_percentage: number; // 占比
}

// 组件引用
const mapRefForDurationStayLocalGuests = ref<any>(null);

// 状态管理
const slideStore = useSlideStore();
const isShowDataPanel = ref(false);
const loaded = ref(false);
const bubbleData = ref<BubbleData[]>([]);
const stayDurationData = ref<any>(null);

// 商圈相关状态
const activeDistrict = ref("文化博览中心");

/**
 * 工具函数：将数字转换为“万”为单位，并保留两位小数
 * @param num - 原始数字（如 123456）
 * @returns 转换后数字（如 12.35）
 */
const formatToWan = (num: number): number => {
  // 1. 除以10000转为万单位；2. toFixed(2)保留两位小数；3. Number()避免字符串类型
  return Number((num / 10000).toFixed(2));
};

// 计算属性：获取所有商圈名称列表（供选择器使用）
const districtNameList = computed<string[]>(() => {
  return stayDurationData.value?.bds_list
    ? stayDurationData.value.bds_list.map((item: BusinessDistrict) => item.bds_name)
    : [];
});

// 计算属性：建立商圈名称到数据的映射
const districtDataMap = computed<Record<string, BusinessDistrict>>(() => {
  if (!stayDurationData.value?.bds_list) return {};

  return stayDurationData.value.bds_list.reduce(
    (map, district) => {
      map[district.bds_name] = district;
      return map;
    },
    {} as Record<string, BusinessDistrict>
  );
});

// 计算属性：当前选中商圈的图表数据（核心：处理count为万单位+两位小数）
const chartData = computed<Array<{ name: string; value: number; count: number }>>(() => {
  const currentDistrict = districtDataMap.value[activeDistrict.value];
  if (!currentDistrict?.sec_list) return [];

  // 固定时间段顺序，确保图表展示一致
  const timeOrder = ["9-11点", "11-14点", "14-17点", "17-22点", "22-次日9点"];

  return currentDistrict.sec_list
    .map((item) => ({
      name: item.sec_name,
      value: Math.round(item.sec_percentage * 100), // 占比转百分比整数（如0.38→38）
      count: formatToWan(item.sec_value) // 核心修改：原始人数转万单位+两位小数
    }))
    .sort((a, b) => timeOrder.indexOf(a.name) - timeOrder.indexOf(b.name));
});

// 图表配置：标题补充单位说明
const chartTitle = ref("商圈消费时间段偏好");

// 处理商圈切换
const handleDistrictChange = (district: string) => {
  // 验证商圈有效性，避免无效切换
  if (districtNameList.value.includes(district)) {
    activeDistrict.value = district;
  }
};

// 生成地图气泡数据
const getBubbleData = (bdsList: BusinessDistrict[]): BubbleData[] => {
  if (!bdsList.length) return [];

  // 按消费人数降序排序
  const sortedList = [...bdsList].sort((a, b) => b.consumer_count - a.consumer_count);

  return sortedList.map((item, index) => ({
    bds_id: item.bds_id,
    value: toWan(item.consumer_count),
    size: index === 0 ? 100 : index === 1 ? 90 : index === 2 ? 80 : 60,
    position: {
      lat: item.bds_latitude,
      lng: item.bds_longitude
    }
  }));
};

// 初始化数据
const initStayDurationData = () => {
  stayDurationData.value = chartDataJson.result;
  const { bds_list = [] } = stayDurationData.value;

  // 生成气泡数据
  bubbleData.value = getBubbleData(bds_list as BusinessDistrict[]);

  // 初始化默认商圈（若默认值不存在则选第一个）
  if (bds_list.length && !districtNameList.value.includes(activeDistrict.value)) {
    activeDistrict.value = bds_list[0].bds_name;
  }
};

// 幻灯片切换逻辑
useSlideHook((slide) => {
  if (slide === SLIDE_COMPONENTS_INDEX["timePreference"]) {
    initStayDurationData();

    isShowDataPanel.value = true;
  } else {
    isShowDataPanel.value = false;
  }
});

// 地图加载回调
const handleMapLoad = (value: boolean) => {
  loaded.value = value;
  slideStore.setLoadMaps("timePreference");
};

// 组件挂载时初始化
onMounted(() => {});

// 暴露组件方法
defineExpose({
  name: "timePreference",
  preloadMap: () => {
    if (loaded.value) {
      slideStore.setLoadMaps("timePreference");
    } else {
      setTimeout(() => {
        mapRefForDurationStayLocalGuests.value?.initMap();
      }, 0);
    }
  }
});
</script>

<style scoped lang="scss">
.data-panel {
  position: absolute;
  top: 30%;
  right: 50px;
  transform: translateY(-30%);
  z-index: 20;
  pointer-events: none;

  .chart-content {
    padding: 20px;
    pointer-events: auto;
  }
}

.data-panel-title {
  font-size: 22px;
  font-weight: 700;
  color: #b1fff2;
  margin: 0 0 30px 0;
  line-height: 22px;
  pointer-events: auto;
}

.chart-section {
  padding: 0 10px;

  .chart-section-title {
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 0 0 10px 0;

    .chart-title-icon {
      width: 18px;
      height: 22px;
    }

    .chart-title {
      font-size: 16px;
      font-weight: 700;
      color: #f7fffe;
      line-height: 1;
      text-shadow: 0px 4px 6px #4d429e;
    }
  }

  .chart-desc {
    font-size: 12px;
    color: #e6e6e6;
    margin: 0 0 20px 0;
    line-height: 18px;
    word-break: break-all;
    text-align: justify;
    text-shadow: 0px 4px 6px #4d429e;
  }

  // 无数据提示样式
  .no-data {
    color: rgba(255, 255, 255, 0.7);
    text-align: center;
    padding: 40px 0;
    font-size: 14px;
  }
}

// 覆盖物悬停效果
.district-overlay {
  &:hover {
    transform: translateY(-4px) scale(1.1);
    box-shadow:
      0 12px 32px rgba(0, 0, 0, 0.7),
      0 0 0 2px rgba(255, 255, 255, 0.4),
      0 0 40px rgba(147, 244, 255, 0.5);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);

    .overlay-icon {
      transform: scale(1.15) rotate(8deg);
      box-shadow: 0 0 24px rgba(147, 244, 255, 0.9);
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);

      > div > div {
        opacity: 0.9;
        transform: scale(1.3);
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      }
    }

    .overlay-text {
      text-shadow:
        0 4px 8px rgba(0, 0, 0, 1),
        0 0 15px rgba(147, 244, 255, 0.6);
      transform: translateX(3px);
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    }
  }
}

.retry-btn {
  background: #93f4ff;
  color: #000;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  margin-top: 15px;
  transition: all 0.3s ease;

  &:hover {
    background: #c3fff6;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
