<template>
  <div class="chart-container">
    <div ref="chartRef" class="chart-wrapper"></div>
  </div>
</template>

<script setup lang="ts">
import * as echarts from "echarts";
import type { ECharts, EChartsOption } from "echarts";

// 1. 定义图表数据类型（新增 rate 字段，匹配父组件传递的占比数据）
interface ChartData {
  categories: string[];
  values: number[];
  rate?: number[]; // 接收父组件的占比数据（用于%场景）
}

// 颜色配置项类型
interface ColorConfig {
  color: string;
}

// 2. 组件属性（新增 unit-type 控制单位）
const props = defineProps<{
  data: ChartData;
  width?: string;
  height?: string;
  animation?: boolean;
  maxValue?: number;
  // 新增：单位类型，可选“万”或“%”
  unitType: "ten-thousand" | "percentage";
}>();

// 默认属性
const defaultData: ChartData = {
  categories: [],
  values: [],
  rate: []
};

// 颜色数组
const colorArray: ColorConfig[] = [
  { color: "#FF90B2" },
  { color: "#FFE995" },
  { color: "#FAFFC3" },
  { color: "#C3FFF6" },
  { color: "#B1FFBA" },
  { color: "#C3FFF6" },
  { color: "#C3E3FF" },
  { color: "#D3C3FF" }
];

// 图表引用
const chartRef = ref<HTMLDivElement>(null);
let chartInstance: ECharts | null = null;

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return;
  if (chartInstance) chartInstance.dispose();
  chartInstance = echarts.init(chartRef.value);
  const option = getChartOption();
  chartInstance.setOption(option);
};

// 3. 获取图表配置选项（核心：重构 tooltip 样式）
const getChartOption = (): EChartsOption => {
  const { categories, values, rate } = props.data || defaultData;
  const { unitType } = props;
  // 处理Y轴最大值：不管什么场景，都用 values 数据
  const targetData = values;
  const maxValue = props.maxValue || (targetData.length ? Math.max(...targetData) * 1.2 : 0);

  return {
    tooltip: {
      // 关键：根据 unitType 决定是否显示 tooltip
      show: unitType === "percentage",
      trigger: "axis",
      axisPointer: { type: "shadow", shadowStyle: { color: "rgba(255, 255, 255, 0.1)" } },
      formatter: (params: any[]) => {
        // 原有自定义 tooltip 逻辑...
        const item = params[0];
        const name = item.name;
        // 数值：根据 unitType 处理展示形式
        const valueText =
          unitType === "percentage"
            ? `${item.value.toFixed(2)}万` // 这里用 values 的原始值，展示为万
            : `${item.value.toFixed(2)}万`;

        let rateText = "";
        if (unitType === "percentage" && props.data.rate) {
          const index = categories.indexOf(name);
          if (index !== -1) {
            rateText = `到访人数占比 ${(props.data.rate[index] * 100).toFixed(1)}%`;
          }
        }

        return `
          <div class="custom-tooltip">
            <div class="tooltip-title">${name}</div>
            <div class="tooltip-row">
              <span>到访人数</span>
              <span class="tooltip-value">${valueText}</span>
            </div>
            ${rateText ? `<div class="tooltip-row">${rateText}</div>` : ""}
          </div>
        `;
      },
      backgroundColor: "#042d53",
      borderColor: "transparent",
      textStyle: { color: "#fff" },
      extraCssText: `
        .custom-tooltip {
          background: rgba(14, 42, 67, 0.9);
          border: 1px solid rgba(255, 255, 255, 0.2);
          border-radius: 8px;
          padding: 12px 16px;
          min-width: 160px;
          box-shadow: 0 2px 8px rgba(0,0,0,0.3);
        }
        .tooltip-title {
          font-size: 16px;
          font-weight: bold;
          margin-bottom: 8px;
          color: #fff;
        }
        .tooltip-row {
          display: flex;
          justify-content: space-between;
          margin-bottom: 4px;
          color: #fff;
        }
        .tooltip-value {
          font-weight: bold;
          color: #FFD700; 
        }
      `
    },
    grid: { left: "3%", top: "0%", right: "10%", bottom: "8%", containLabel: true },
    xAxis: {
      type: "value",
      show: false,
      position: "top",
      max: maxValue,
      splitNumber: 5,
      axisLine: { show: false, lineStyle: { color: "rgba(255, 255, 255, 0.1)" } },
      axisTick: { show: false },
      splitLine: { show: true, lineStyle: { color: "rgba(255, 255, 255, 0.05)" } },
      axisLabel: { show: true, color: "rgba(255, 255, 255, 0.6)", fontSize: 12 }
    },
    yAxis: {
      type: "category",
      inverse: true,
      axisLine: { show: false },
      axisTick: { show: false },
      splitLine: { show: false },
      axisLabel: { show: true, color: "#FFFFFF", fontSize: 14, padding: [0, 10, 0, 0] },
      data: categories
    },
    series: [
      {
        name: "",
        type: "bar",
        barWidth: 10,
        barGap: "0%",
        barCategoryGap: "40%",
        // 系列数据：不管什么场景，都用 values
        data: values,
        label: {
          show: true,
          position: "right",
          // 标签单位动态显示
          formatter: (params: any) => {
            if (unitType === "percentage") {
              // 找到当前数据对应的 rate 值，展示为百分比
              const index = params.dataIndex;
              return rate && rate[index] ? `${(rate[index] * 100).toFixed(2)}%` : "";
            } else {
              return `${params.value.toFixed(2)}万`;
            }
          },
          distance: 10,
          textStyle: { color: "white", fontSize: 13, fontWeight: "500" }
        },
        itemStyle: {
          color: (params: any) => {
            const num = colorArray.length;
            return colorArray[params.dataIndex % num].color;
          },
          barBorderRadius: [10, 10, 10, 10],
          borderWidth: 0.5,
          borderColor: "rgba(255, 255, 255, 0.2)"
        }
      }
    ]
  };
};

// 监听数据和单位变化，更新图表
watch(
  [() => props.data, () => props.unitType],
  () => {
    if (chartInstance) chartInstance.setOption(getChartOption());
  },
  { deep: true }
);

// 监听尺寸变化
watch([() => props.width, () => props.height], () => {
  chartInstance?.resize();
});

// 组件生命周期
onMounted(() => initChart());

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }
});

defineExpose({
  initChart: () => initChart(),
  chartInstance: () => chartInstance
});
</script>

<style scoped>
.chart-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.chart-wrapper {
  width: 100%;
  height: 278px;
}
</style>
