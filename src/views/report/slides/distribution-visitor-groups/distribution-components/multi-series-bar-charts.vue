<template>
  <div class="multi-series-bar-chart" ref="chartRef" style="width: 100%; height: 100%"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from "vue";
import * as echarts from "echarts";

interface ChartDataItem {
  name: string;
  value: number;
  percentage: number;
}

interface SeriesData {
  name: string;
  data: ChartDataItem[];
  color?: string;
}

interface ChartProps {
  // 图表数据
  seriesData: SeriesData[];
  // 图表标题
  title?: string;
  // 图表副标题
  subtitle?: string;
  // 自定义颜色
  colors?: string[];
  // 图表高度
  height?: string | number;
  // 是否显示图例
  showLegend?: boolean;
  // 是否显示网格线
  showGrid?: boolean;
  // 是否显示数据标签
  showLabel?: boolean;
  // 是否显示提示框
  showTooltip?: boolean;
  // 自定义配置项
  customOptions?: any;
  // 提示框类型名称
  tipTypeName?: string[];
  // 提示框前缀
  beforeName?: string;
  // 提示框后缀
  afterName?: string;
  // 是否显示图例
  showLegend?: boolean;
  // 图例名称
  legendNames?: string[];
  // 是否显示千位分隔符
  isThousandSeparator?: boolean;
}

const props = withDefaults(defineProps<ChartProps>(), {
  title: "",
  subtitle: "",
  height: "100%",
  showLegend: true,
  showGrid: true,
  showLabel: false,
  showTooltip: true,
  colors: () => [
    "#5470c6",
    "#91cc75",
    "#fac858",
    "#ee6666",
    "#73c0de",
    "#3ba272",
    "#fc8452",
    "#9a60b4"
  ],
  customOptions: () => ({}),
  tipTypeName: () => [
    ["数值", "占比"],
    ["数值", "占比"]
  ],
  beforeName: "",
  afterName: "",
  legendNames: () => ["", ""],
  isThousandSeparator: true
});

const emit = defineEmits<{
  chartClick: [params: any];
  chartReady: [chart: echarts.ECharts];
}>();

const chartRef = ref<HTMLElement>();
let chartInstance: echarts.ECharts | null = null;

// 默认配置
const getDefaultOptions = (): echarts.EChartsOption => {
  return {
    title: {
      text: props.title,
      subtext: props.subtitle,
      left: "center",
      textStyle: {
        fontSize: 16,
        fontWeight: "bold",
        color: "#333"
      },
      subtextStyle: {
        fontSize: 12,
        color: "#666"
      }
    },
    tooltip: {
      show: true,
      trigger: "item"
    },
    legend: {
      show: props.showLegend,
      right: "10%",
      icon: "circle",
      itemWidth: 10,
      itemHeight: 10,
      textStyle: {
        fontSize: 14,
        color: "#EFF4FF"
      }
    },
    grid: {
      left: "10%",
      right: "10%",
      bottom: "10%",
      top: props.title ? "20%" : "15%",
      containLabel: true
    },
    xAxis: {
      type: "category",
      data: props.seriesData[0]?.map((item) => item.name) || [],
      axisLine: {
        lineStyle: {
          color: "#98A1B1"
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        fontSize: 14,
        color: "#FFFFFF",
        interval: 0,
        rotate: 0
      }
    },
    yAxis: {
      type: "value",
      min: 0,
      max: 100,
      interval: 20,
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        fontSize: 14,
        fontWeight: "500",
        color: "#BDC6D8",
        formatter: "{value}%"
      },
      splitLine: {
        show: props.showGrid,
        lineStyle: {
          color: "#98A1B1",
          type: "solid"
        }
      }
    },
    series: props.seriesData.map((series, index) => ({
      name: props.legendNames[index],
      type: "bar",
      data: series.map((item) => item.percentage),
      itemStyle: {
        color: series.color || props.colors[index % props.colors.length],
        borderRadius: 8
      },
      barWidth: "16px",
      barGap: "10%",
      tooltip: {
        backgroundColor: "rgba(31, 26, 34, 0.9)",
        borderColor: "rgba(0, 0, 0, 0)",
        borderWidth: 1,
        formatter: (params: any) => {
          const valueNum = series[params.componentIndex]?.value;
          const labelValue = props.isThousandSeparator
            ? `${(valueNum / 10000).toFixed(2)}`
            : valueNum.toString();
          return `<div style="display: flex; flex-direction: column; padding: 2px;">
          <div style="font-size: 12px; color: #fff;">${props.beforeName ? `${props.beforeName}：` : ""}${params.name}${props.afterName ? `${props.afterName}` : ""}</div>
          <div style="margin: 6px 0;font-size: 12px; display: flex; justify-content: space-between; align-items: center;">
            <span style="color: #E2F6FE; padding-right: 10px;">${props.tipTypeName?.[index]?.[0]}</span>
            <span style="color: ${props.colors[params.seriesIndex]}; font-size: 12px; font-weight: 600;">${labelValue}${props.isThousandSeparator ? "万" : ""}</span>
          </div>
          <div style="font-size: 12px; display: flex; justify-content: space-between; align-items: center;">
            <span style="color: #E2F6FE; padding-right: 10px;">${props.tipTypeName?.[index]?.[1]}</span>
            <span style="color: ${props.colors[params.seriesIndex]}; font-size: 12px; font-weight: 600;">${params.value}%</span>
          </div>
        </div>`;
        }
      },
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowColor: "rgba(0,0,0,0.3)"
        }
      }
    }))
  };
};

// 合并配置
const mergeOptions = (
  defaultOptions: echarts.EChartsOption,
  customOptions: any
): echarts.EChartsOption => {
  return {
    ...defaultOptions,
    ...customOptions,
    series: defaultOptions.series
  };
};

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return;

  chartInstance = echarts.init(chartRef.value, null, {
    renderer: "svg"
  });

  const defaultOptions = getDefaultOptions();
  const finalOptions = mergeOptions(defaultOptions, props.customOptions);

  chartInstance.setOption(finalOptions);

  // 绑定事件
  chartInstance.on("click", (params) => {
    emit("chartClick", params);
  });

  emit("chartReady", chartInstance);
};

// 更新图表
const updateChart = () => {
  if (!chartInstance) return;

  const defaultOptions = getDefaultOptions();
  const finalOptions = mergeOptions(defaultOptions, props.customOptions);

  chartInstance.setOption(finalOptions, true);
};

// 监听数据变化
watch(
  () => props.seriesData,
  () => {
    nextTick(() => {
      updateChart();
    });
  },
  { deep: true }
);

// 监听自定义配置变化
watch(
  () => props.customOptions,
  () => {
    nextTick(() => {
      updateChart();
    });
  },
  { deep: true }
);

// 监听窗口大小变化
const handleResize = () => {
  chartInstance?.resize();
};

// 暴露方法给父组件
defineExpose({
  getChart: () => chartInstance,
  resize: () => chartInstance?.resize(),
  update: updateChart
});

onMounted(() => {
  nextTick(() => {
    initChart();
  });

  window.addEventListener("resize", handleResize);
});

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }
  window.removeEventListener("resize", handleResize);
});
</script>

<style scoped>
.multi-series-bar-chart {
  width: 100%;
  height: v-bind(height);
}
</style>
