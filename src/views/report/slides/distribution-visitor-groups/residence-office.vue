<template>
  <layout-main>
    <div class="content-container full flex justify-center relative" @dblclick="handleClick">
      <div class="full bg-img"></div>
      <!-- 图表内容区域 -->
      <div class="chart-container flex-col flex-justify-between">
        <div class="title-container w-full flex flex-col items-center">
          <div class="title-text-container">
            <div class="title-text">{{ chartTitle }}</div>
          </div>
          <div class="title-desc-container">
            <div class="title-desc-text">{{ chartTitleDesc }}</div>
          </div>
        </div>
        <div class="chart-box">
          <MultiSeriesBarCharts
            v-if="workPopulationData.length > 0 || dwellPopulationData.length > 0"
            :series-data="[dwellPopulationData, workPopulationData]"
            :colors="['#7F94F7', '#4CFBFB']"
            :tip-type-name="[
              ['居住人数', '居住人数占比'],
              ['工作人数', '工作人数占比']
            ]"
            :legend-names="['居住人数', '工作人数']"
          />
        </div>
      </div>
    </div>
  </layout-main>
</template>

<script lang="ts" setup>
import { BusinessDistrictEnum } from "@/views/report/components/enum";
import MultiSeriesBarCharts from "./distribution-components/multi-series-bar-charts.vue";
import { SLIDE_COMPONENTS_INDEX } from "@/views/report/config";
import { useSlideHook } from "../../hooks/useSlideHook";
import chartDataJson from "@/data/visitor-source-destination/常驻人口分布分析.json";

// 图表标题和副标题
const chartTitle = ref("常驻人口分布分析");
const chartTitleDesc = ref("");

// 办公人口数据
const workPopulationData = ref<any>([]);
// 居住人口数据
const dwellPopulationData = ref<any>([]);

// 清理图表数据
const clearChartData = () => {
  workPopulationData.value = [];
  dwellPopulationData.value = [];
};

const updateChartData = () => {
  const chartData = chartDataJson.result;
  chartTitleDesc.value = chartData.page_conclusion;
  chartData.bds_list.forEach((item: any) => {
    workPopulationData.value.push({
      name: item.bds_name,
      value: item.work_population,
      percentage: Math.round(item.work_percentage * 100)
    });
    dwellPopulationData.value.push({
      name: item.bds_name,
      value: item.dwell_population,
      percentage: Math.round(item.dwell_percentage * 100)
    });
  });
};

const handleClick = () => {
  document.documentElement.requestFullscreen();
};

useSlideHook((slide) => {
  if (slide === SLIDE_COMPONENTS_INDEX["residenceOffice"]) {
    // 幻灯片到达当前页面时，直接使用预处理的数据
    updateChartData();
  } else {
    // 幻灯片离开时清理数据
    clearChartData();
  }
});
</script>

<style lang="scss" scoped>
.bg-img {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url("@/assets/visitor-group-bg.png") no-repeat;
  background-size: 100% 100%;
  background-position: center;
  filter: brightness(0.4);
}
.chart-container {
  width: 100%;
  height: calc(100% - 180px);
  padding-top: 60px;
  position: relative;
}
.title-container {
  display: flex;
  flex-direction: column;
  align-items: center;

  .title-desc-container {
    max-width: 710px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }

  .title-text {
    font-size: 22px;
    font-weight: 700;
    color: #b1fff2;
  }
  .title-desc-text {
    margin-top: 14px;
    font-size: 14px;
    font-weight: 400;
    color: #fff;
  }
}
.chart-box {
  width: 100%;
  height: calc(100% - 90px);
  padding: 20px 5% 0;

  .chart-layout {
    margin-top: 34px;

    .chart-item {
      .chart-item-title {
        margin-bottom: 35px;
        display: flex;
        align-items: center;

        .chart-title-icon {
          width: 18px;
          height: 23px;
          margin-right: 10px;
        }
        .chart-title-text {
          font-size: 16px;
          font-weight: 700;
          color: #f7fffe;
          text-shadow: 0px 4px 6px #4d429e;
        }
      }
    }
  }
}
</style>
