<template>
  <layout-main>
    <div class="duration-stay-container">
      <!-- 地图组件 -->
      <bds-map
        ref="durationStayLocalGuestsMap"
        :showBubble="true"
        show-name
        :bubble-data="bubbleData"
        @load="handleMapLoad"
      />

      <!-- 右侧数据面板 -->
      <div class="data-panel" v-if="isShowDataPanel && stayDurationData">
        <div class="data-panel-title">常驻人口分布分析</div>
        <card-slot card-type="cyan" :width="470" :height="465">
          <div class="chart-content">
            <div class="chart-section">
              <div class="chart-section-title">
                <img src="@/assets/icon.png" alt="图表标题图标" class="chart-title-icon" />
                <h4 class="chart-title">{{ chartTitle }}</h4>
              </div>
              <p class="chart-desc">
                {{ stayDurationData?.page_conclusion || "" }}
              </p>
              <!-- 无数据状态处理 -->
              <div v-if="!chartData.categories.length" class="no-data">暂无常住人口数据</div>
              <bar-chart v-else ref="echartsChart" :data="chartData" :unit-type="'ten-thousand'" />
            </div>
          </div>
        </card-slot>
      </div>
    </div>
  </layout-main>
</template>

<script setup lang="ts">
import barChart from "./distribution-components/bar-chart.vue";
import CardSlot from "../../components/card-slot.vue";
import { SLIDE_COMPONENTS_INDEX } from "@/views/report/config";
import { useSlideHook } from "../../hooks/useSlideHook";
import chartDataJson from "@/data/visitor-source-destination/常驻人口分析.json";
import { toWan } from "@/utils";
import { useSlideStore } from "@/store/modules/slide";
import { BubbleData } from "@/views/report/components/bds-map.vue";

// 类型定义
interface BusinessDistrict {
  bds_id: string;
  bds_name: string;
  bds_conclusion: string;
  resident_population: number;
  bds_latitude: number;
  bds_longitude: number;
}

interface ChartDataType {
  categories: string[];
  values: number[];
}

// 组件引用
const durationStayLocalGuestsMap = ref<any>(null);
const echartsChart = ref<any>(null);

// 状态管理
const slideStore = useSlideStore();
const isShowDataPanel = ref(false);
const stayDurationData = ref<any>(null);
const loaded = ref(false);
const bubbleData = ref<BubbleData[]>([]);

// 图表配置
const chartTitle = ref("商圈的常住人口");
const chartData = ref<ChartDataType>({
  categories: [],
  values: []
});

/**
 * 转换数字为万单位并保留两位小数
 */
const formatToWan = (num: number): number => {
  return Number((num / 10000).toFixed(2));
};

// 处理图表数据
const processChartData = (bdsList: BusinessDistrict[]) => {
  const sortedList = [...bdsList].sort((a, b) => b.resident_population - a.resident_population);

  return {
    categories: sortedList.map((item) => item.bds_name),
    values: sortedList.map((item) => formatToWan(item.resident_population))
  };
};

// 生成地图气泡数据
const getBubbleData = (bdsList: BusinessDistrict[]): BubbleData[] => {
  if (!bdsList.length) return [];

  const sortedList = [...bdsList].sort((a, b) => b.resident_population - a.resident_population);

  return sortedList.map((item, index) => ({
    bds_id: item.bds_id,
    value: toWan(item.resident_population),
    size: index === 0 ? 100 : index === 1 ? 90 : index === 2 ? 80 : 60,
    position: {
      lat: item.bds_latitude,
      lng: item.bds_longitude
    }
  }));
};

// 初始化数据（修复核心错误）
const initStayDurationData = () => {
  stayDurationData.value = chartDataJson.result;
  // 从数据中获取 bds_list（下划线命名）并赋值给变量 bdsList
  const { bds_list: bdsList = [] } = stayDurationData.value;

  // 处理图表数据（使用正确的变量 bdsList）
  chartData.value = processChartData(bdsList as BusinessDistrict[]);

  // 生成地图气泡数据（使用正确的变量 bdsList）
  bubbleData.value = getBubbleData(bdsList as BusinessDistrict[]);
};

// 幻灯片切换逻辑
useSlideHook((slide) => {
  if (slide === SLIDE_COMPONENTS_INDEX["residentPopulation"]) {
    isShowDataPanel.value = true;
    initStayDurationData();

    nextTick(() => {
      setTimeout(() => {
        if (echartsChart.value && chartData.value.categories.length) {
          echartsChart.value.initChart();
        }
      }, 100);
    });
  } else {
    isShowDataPanel.value = false;
    if (echartsChart.value?.chartInstance()) {
      echartsChart.value.chartInstance().dispose();
    }
  }
});

// 地图加载回调
const handleMapLoad = (value: boolean) => {
  loaded.value = value;
  slideStore.setLoadMaps("residentPopulation");
};

// 组件挂载时初始化
onMounted(() => {});

// 暴露组件方法
defineExpose({
  name: "residentPopulation",
  preloadMap: () => {
    if (loaded.value) {
      slideStore.setLoadMaps("residentPopulation");
    } else {
      setTimeout(() => {
        durationStayLocalGuestsMap.value?.initMap();
      }, 0);
    }
  }
});
</script>

<style scoped lang="scss">
/* 样式部分保持不变 */
.duration-stay-container {
  position: relative;
  width: 100%;
  height: 100%;
  background-color: #000;
}

.data-panel {
  position: absolute;
  top: 30%;
  right: 50px;
  transform: translateY(-30%);
  z-index: 20;
  pointer-events: none;

  .chart-content {
    padding: 20px;
    pointer-events: auto;
  }
}

.data-panel-title {
  font-size: 22px;
  font-weight: 700;
  color: #b1fff2;
  margin: 0 0 30px 0;
  line-height: 22px;
  pointer-events: auto;
}

.chart-section {
  padding: 0 10px;

  .chart-section-title {
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 0 0 10px 0;

    .chart-title-icon {
      width: 18px;
      height: 22px;
    }

    .chart-title {
      font-size: 16px;
      font-weight: 700;
      color: #f7fffe;
      line-height: 1;
      text-shadow: 0px 4px 6px #4d429e;
    }
  }

  .chart-desc {
    font-size: 12px;
    color: #e6e6e6;
    margin: 0 0 20px 0;
    line-height: 18px;
    word-break: break-all;
    text-align: justify;
    text-shadow: 0px 4px 6px #4d429e;
  }

  .no-data {
    color: rgba(255, 255, 255, 0.7);
    text-align: center;
    padding: 60px 0;
    font-size: 14px;
  }
}

.district-overlay {
  &:hover {
    transform: translateY(-4px) scale(1.1);
    box-shadow:
      0 12px 32px rgba(0, 0, 0, 0.7),
      0 0 0 2px rgba(255, 255, 255, 0.4),
      0 0 40px rgba(147, 244, 255, 0.5);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);

    .overlay-icon {
      transform: scale(1.15) rotate(8deg);
      box-shadow: 0 0 24px rgba(147, 244, 255, 0.9);
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);

      > div > div {
        opacity: 0.9;
        transform: scale(1.3);
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      }
    }

    .overlay-text {
      text-shadow:
        0 4px 8px rgba(0, 0, 0, 1),
        0 0 15px rgba(147, 244, 255, 0.6);
      transform: translateX(3px);
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    }
  }
}

.retry-btn {
  background: #93f4ff;
  color: #000;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  margin-top: 15px;
  transition: all 0.3s ease;

  &:hover {
    background: #c3fff6;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
