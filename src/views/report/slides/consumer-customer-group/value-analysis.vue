<template>
  <layout-main>
    <div class="content-container full flex justify-center relative" @dblclick="handleClick">
      <div class="full bg-img"></div>
      <!-- 图表内容区域 -->
      <div class="chart-container flex-col flex-justify-between">
        <div class="title-container w-full flex flex-col items-center">
          <div class="title-text-container">
            <div class="title-text">{{ chartTitle }}</div>
          </div>
          <div class="title-desc-container">
            <div class="title-desc-text">{{ chartTitleDesc }}</div>
          </div>
        </div>
        <div class="chart-box w-full">
          <div class="tag-boxw-full">
            <BusinessDistrictTags :default-active="currentDistrict" @tag-change="handleTagChange" />
          </div>
          <div class="chart-layout w-full flex flex-justify-between items-center">
            <div class="chart-item spending-power-chart">
              <div class="chart-item-title">
                <img src="@/assets/icon.png" alt="chart-title-icon" class="chart-title-icon" />
                <span class="chart-title-text">{{ spendingPowerChartTitle }}</span>
              </div>
              <CardSlot card-type="purple" :width="420" :height="370">
                <ConcentricPieChart
                  v-if="spendingPowerChartData.length > 0"
                  :data="spendingPowerChartData"
                  :config="{
                    width: 420,
                    height: 370,
                    legendItemGap: 30,
                    colors: ['#BB7AF9', '#3B82F6', '#53FEFF'],
                    radius: ['15%', '55%'],
                    legendDistance: 25,
                    tipTypeName: ['消费人数', '消费人数占比']
                  }"
                />
              </CardSlot>
            </div>
            <div class="chart-item consumption-frequency-chart">
              <div class="chart-item-title">
                <img src="@/assets/icon.png" alt="chart-title-icon" class="chart-title-icon" />
                <span class="chart-title-text">{{ consumptionFrequencyChartTitle }}</span>
              </div>
              <CardSlot card-type="purple" :width="420" :height="370">
                <ConcentricPieChart
                  v-if="consumptionFrequencyChartData.length > 0"
                  :data="consumptionFrequencyChartData"
                  :config="{
                    width: 420,
                    height: 370,
                    legendItemGap: 30,
                    colors: ['#BB7AF9', '#3B82F6', '#53FEFF'],
                    radius: ['15%', '55%'],
                    legendDistance: 25,
                    tipTypeName: ['消费人数', '消费人数占比']
                  }"
                />
              </CardSlot>
            </div>
            <div class="chart-item average-transaction-chart">
              <div class="chart-item-title">
                <img src="@/assets/icon.png" alt="chart-title-icon" class="chart-title-icon" />
                <span class="chart-title-text">{{ averageTransactionChartTitle }}</span>
              </div>
              <CardSlot card-type="purple" :width="420" :height="370">
                <BarLineChart
                  v-if="averageTransactionBarData.length > 0"
                  :bar-data="averageTransactionBarData"
                  :bar-colors="['#50CDFF']"
                  :value-color-index="0"
                  :enable-3d="false"
                  :width="'100%'"
                  :height="'100%'"
                  :custom-options="{
                    grid: {
                      left: '15%',
                      right: '10%',
                      bottom: '12%',
                      top: '18%'
                    },
                    legend: {
                      show: false
                    }
                  }"
                  :tip-type-name="['消费人数', '消费人数占比']"
                  before-name="消费客单价"
                  barDataName="%"
                />
              </CardSlot>
            </div>
          </div>
        </div>
      </div>
    </div>
  </layout-main>
</template>

<script lang="ts" setup>
import { BusinessDistrictEnum } from "@/views/report/components/enum";
import BusinessDistrictTags from "@/views/report/components/business-district-tags.vue";
import CardSlot from "@/views/report/components/card-slot.vue";
import ConcentricPieChart from "@/views/report/components/concentric-pie-chart.vue";
import BarLineChart from "@/views/report/components/bar-line-chart.vue";
import { SLIDE_COMPONENTS_INDEX } from "@/views/report/config";
import { useSlideHook } from "../../hooks/useSlideHook";
import chartDataJson from "@/data/consumer-basic-traits/消费客群价值分析.json";

const currentDistrict = ref<string>(BusinessDistrictEnum["文化博览中心"]);

// 图表标题和副标题
const chartTitle = ref("客群消费价值分析");
const chartTitleDesc = ref("");

const spendingPowerChartTitle = ref("消费能力占比");
const spendingPowerChartData = ref<any>([]);
const consumptionFrequencyChartTitle = ref("消费频次占比");
const consumptionFrequencyChartData = ref<any>([]);
const averageTransactionChartTitle = ref("客单价占比");
// 客单价柱形图和折线图数据
const averageTransactionBarData = ref<Array<{ name: string; value: number }>>([]);

// 预处理的商圈数据，包含所有商圈的数据
const processedDistrictData = ref<Record<string, any>>({});

// 更新图表数据
const updateChartData = () => {
  const currentData = processedDistrictData.value[currentDistrict.value];
  if (currentData) {
    spendingPowerChartData.value = currentData.spendingPower;
    consumptionFrequencyChartData.value = currentData.consumptionFrequency;
    averageTransactionBarData.value = currentData.averageTransaction;
  }
};

// 清理图表数据
const clearChartData = () => {
  spendingPowerChartData.value = [];
  consumptionFrequencyChartData.value = [];
  averageTransactionBarData.value = [];
};

const initData = () => {
  // 预处理所有商圈的数据，避免重复处理
  const bdsList = chartDataJson.result.bds_list;
  chartTitleDesc.value = chartDataJson.result.page_conclusion;

  bdsList.forEach((bds) => {
    const districtName = bds.bds_name;

    // 处理消费能力数据
    const spendingPowerData = bds.consume_power.map((item: any) => ({
      name: item.sec_name,
      extraName: "占比",
      value: item.sec_value,
      percentage: item.sec_percentage
    }));

    // 处理消费频次数据
    const consumptionFrequencyData = bds.consume_frequency.map((item: any) => ({
      name: item.sec_name,
      extraName: "占比",
      value: item.sec_value,
      percentage: item.sec_percentage
    }));

    // 处理客单价数据
    const averageTransactionData = bds.consume_transaction.map((item: any) => ({
      name: item.sec_name,
      value: (item.sec_percentage * 100).toFixed(2),
      count: item.sec_value
    }));

    // 存储处理后的数据
    processedDistrictData.value[districtName] = {
      spendingPower: spendingPowerData,
      consumptionFrequency: consumptionFrequencyData,
      averageTransaction: averageTransactionData
    };
  });
};

const handleTagChange = (tag: string) => {
  // 切换商圈时，先清理数据，再更新数据
  clearChartData();
  currentDistrict.value = tag;
  // 切换商圈时直接使用预处理的数据
  nextTick(() => {
    updateChartData();
  });
};

const handleClick = () => {
  document.documentElement.requestFullscreen();
};

useSlideHook((slide) => {
  if (slide === SLIDE_COMPONENTS_INDEX["valueAnalysis"]) {
    initData();
    nextTick(() => {
      updateChartData();
    });
  } else {
    // 幻灯片离开时清理数据
    clearChartData();
  }
});
</script>

<style lang="scss" scoped>
.bg-img {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url("@/assets/visitor-group-bg.png") no-repeat;
  background-size: 100% 100%;
  background-position: center;
  filter: brightness(0.4);
}
.chart-container {
  width: 100%;
  height: calc(100% - 180px);
  padding-top: 60px;
  position: relative;
}
.title-container {
  display: flex;
  flex-direction: column;
  align-items: center;

  .title-desc-container {
    max-width: 710px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }

  .title-text {
    font-size: 22px;
    font-weight: 700;
    color: #b1fff2;
  }
  .title-desc-text {
    margin-top: 14px;
    font-size: 14px;
    font-weight: 400;
    color: #fff;
  }
}
.chart-box {
  width: 100%;
  height: calc(100% - 90px);
  padding: 20px 5% 0;

  .chart-layout {
    margin-top: 34px;

    .chart-item {
      .chart-item-title {
        margin-bottom: 35px;
        display: flex;
        align-items: center;

        .chart-title-icon {
          width: 18px;
          height: 23px;
          margin-right: 10px;
        }
        .chart-title-text {
          font-size: 16px;
          font-weight: 700;
          color: #f7fffe;
          text-shadow: 0px 4px 6px #4d429e;
        }
      }
    }

    .spending-power-chart {
      // 消费能力图表样式
    }

    .consumption-frequency-chart {
      // 消费频次图表样式
    }

    .average-transaction-chart {
      // 客单价图表样式
    }
  }
}
</style>
