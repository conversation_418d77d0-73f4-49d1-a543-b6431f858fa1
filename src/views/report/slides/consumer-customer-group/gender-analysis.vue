<template>
  <layout-main>
    <div class="content-container full flex justify-center relative" @dblclick="handleClick">
      <div class="full bg-img"></div>
      <div class="absolute top-20 right-10 z-1000">
        <!-- 商业区标签导航 -->
        <BusinessDistrictTags :default-active="currentDistrict" @tag-change="handleTagChange" />
      </div>
      <div class="middle">
        <div class="title">{{ chartTitle }}</div>
        <div class="subtitle">{{ chartSubtitle }}</div>
        <div class="chart-box">
          <div class="left-chart-box">
            <div class="chart-box-title-box">
              <img src="@/assets/icon.png" alt="" class="icon-img" />
              <div class="icon-title">消费人次占比</div>
            </div>
            <div class="chart-box-chart-bg">
              <div class="card-bg">
                <genderPieChart :data="consume_count" ref="genderPieChartCount" />
              </div>
            </div>
          </div>
          <div class="right-chart-box">
            <div class="chart-box-title-box">
              <img src="@/assets/icon.png" alt="" class="icon-img" />
              <div class="icon-title">消费金额占比</div>
            </div>
            <div class="chart-box-chart-bg">
              <div class="card-bg">
                <genderPieChart :data="consume_amount" ref="genderPieChartAmount" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </layout-main>
</template>

<script setup lang="ts">
import consumer from "@/data/consumer-basic-traits/商圈消费客群性别分析.json";
import { BusinessDistrictEnum } from "@/views/report/components/enum";
import BusinessDistrictTags from "@/views/report/components/business-district-tags.vue";
const currentDistrict = ref<string>(BusinessDistrictEnum["文化博览中心"]);
import genderPieChart from "@/views/report/components/genderPieChart.vue";
const chartData = ref<any>(null);
const consume_count = ref<any>([]);
const consume_amount = ref<any>([]);
// 图表标题和副标题
const chartTitle = ref("消费客群性别分布分析");
const chartSubtitle = ref(
  "商圈消费客群不同年龄阶段的消费人次占比、消费金额占比,商圈消费客群不同年龄阶段的消费人次占比、消费金额占比"
);
import { SLIDE_COMPONENTS_INDEX } from "@/views/report/config";
import { useSlideHook } from "../../hooks/useSlideHook";
const genderPieChartCount = ref<any>(null);
const genderPieChartAmount = ref<any>(null);

useSlideHook((slide) => {
  if (slide === SLIDE_COMPONENTS_INDEX["genderAnalysis"]) {
    genderPieChartCount.value?.initChart();
    genderPieChartAmount.value?.initChart();
  } else {
    genderPieChartCount.value?.myChart()?.dispose();
    genderPieChartAmount.value?.myChart()?.dispose();
  }
});

// 获取JSON数据
const fetchChartData = async () => {
  try {
    const data = consumer;
    chartData.value = data.result; // 注意这里使用 data.result

    // // 更新副标题
    if (data.result) {
      // 使用第一个商业区的结论作为副标题
      if (data.result.bds_list && data.result.bds_list.length > 0) {
        chartSubtitle.value = data.result.page_conclusion;
        const consumeCountEchart = data.result.bds_list[0].consume_count || [];
        consume_count.value = consumeCountEchart.map((item: any) => {
          return {
            name: item.sex,
            value: item.count
          };
        });
        const consumeAmountEchart = data.result.bds_list[0].consume_amount || [];
        consume_amount.value = consumeAmountEchart.map((item: any) => {
          return {
            name: item.sex,
            value: item.amount
          };
        });
      }
    }
  } catch (error) {
    console.error("获取数据失败:", error);
  } finally {
    // loading.value = false;
  }
};
const handleClick = () => {
  document.documentElement.requestFullscreen();
};
const handleTagChange = (value: string) => {
  currentDistrict.value = value;
  for (let index = 0; index < chartData.value.bds_list.length; index++) {
    if (chartData.value.bds_list[index].bds_name === value) {
      // chartSubtitle.value = chartData.value.bds_list[index].bds_conclusion || "";
      const consumeCountEchart = chartData.value.bds_list[index].consume_count || [];
      consume_count.value = consumeCountEchart.map((item: any) => {
        return {
          name: item.sex,
          value: item.count
        };
      });
      const consumeAmountEchart = chartData.value.bds_list[index].consume_amount || [];
      consume_amount.value = consumeAmountEchart.map((item: any) => {
        return {
          name: item.sex,
          value: item.amount
        };
      });
      break;
    }
  }
  console.log(consume_count.value, " consume_count.value");
};
onMounted(() => {
  fetchChartData();
});
</script>

<style lang="scss" scoped>
.content-container {
  padding-top: 60px;
}
.relative {
  position: relative;
  width: 100%;
  .middle {
    position: absolute;
    top: 86px;
    left: 0;
    text-align: left;
    width: 100%;
    padding: 0 0 0 5%;
    // padding: 0 0 0 10%;
    box-sizing: border-box;
    .title {
      font-family: Verdana;
      font-size: 26px;
      font-weight: bold;
      line-height: 26px;
      letter-spacing: 0em;
      color: #b1fff2;
      margin-bottom: 19px;
    }
    .subtitle {
      font-family: Verdana;
      font-size: 12px;
      font-weight: normal;
      line-height: 18px;
      letter-spacing: 0em;
      font-variation-settings: "opsz" auto;
      color: #d7d7d7;
      text-shadow: 0px 4px 6px #4d429e;
      margin-bottom: 32px;
      // margin-bottom: 78px;
    }
    .chart-box {
      display: flex;
      // padding: 0 0 0 3%;

      .chart-box-title-box {
        display: flex;
        margin-bottom: 50px;
        .icon-img {
          width: 17.82px;
          height: 23.35px;
          margin: 0 12.18px 0 0 !important;
        }
        .icon-title {
          font-family: Verdana;
          font-size: 16px;
          font-weight: bold;
          letter-spacing: 0em;
          color: #f7fffe;
          text-shadow: 0px 4px 6px #4d429e;
          margin-top: 2px;
        }
      }
      .right-chart-box {
        margin-left: 230px;
      }
      .chart-box-chart-bg {
        // width: 363px;
        width: 463px;
        // height: 300px;
        height: 400px;
        background: url("@/assets/gender-bg.png") no-repeat;
        background-size: 100% 100%;
        // margin-left: 30px;
        position: relative;
      }
      .card-bg {
        width: 99%;
        height: 98.6%;
        background: linear-gradient(
          141deg,
          rgba(40, 41, 92, 0.003) 3%,
          rgba(17, 9, 39, 0.003) 106%
        );
        backdrop-filter: blur(20px);
        border-radius: 12px;
        position: absolute;
        left: 2px;
        top: 3px;
      }
      //   .left-chart-box {
      //     width: 363px;
      //     height: 300px;
      //     background: url("@/assets/gender-bg.png") no-repeat;
      //     background-size: 100% 100%;
      //   }
      //   .right-chart-box {
      //     width: 363px;
      //     height: 300px;
      //     background: url("@/assets/gender-bg.png") no-repeat;
      //     background-size: 100% 100%;
      //   }
    }
  }
}
.bg-img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url("@/assets/visitor-group-bg.png") no-repeat;
  background-size: 100% 100%;
  background-position: center;
  filter: brightness(0.4);
}
</style>