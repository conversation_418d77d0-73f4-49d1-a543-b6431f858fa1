<template>
  <layout-main>
    <div class="content-container full flex justify-center relative" @dblclick="handleClick">
      <div class="full bg-img"></div>
      <!-- 图表内容区域 -->
      <div class="chart-container flex-col flex-justify-between">
        <div class="title-container w-full flex flex-col items-center">
          <div class="title-text-container">
            <div class="title-text">{{ chartTitle }}</div>
          </div>
          <div class="title-desc-container">
            <div class="title-desc-text">{{ chartTitleDesc }}</div>
          </div>
        </div>
        <div class="chart-box w-full">
          <div class="tag-boxw-full">
            <BusinessDistrictTags :default-active="currentDistrict" @tag-change="handleTagChange" />
          </div>
          <div class="chart-layout w-full flex flex-justify-between items-center">
            <div class="chart-item marriage-chart">
              <div class="chart-item-title">
                <img src="@/assets/icon.png" alt="chart-title-icon" class="chart-title-icon" />
                <span class="chart-title-text">{{ marriageChartTitle }}</span>
              </div>
              <CardSlot card-type="purple" :width="420" :height="370">
                <ConcentricPieChart
                  v-if="marriageChartData.length > 0"
                  :data="marriageChartData"
                  :config="{
                    width: 420,
                    height: 370,
                    legendItemGap: 30,
                    colors: ['#BB7AF9', '#3B82F6'],
                    radius: ['15%', '55%'],
                    legendDistance: 25,
                    tipTypeName: ['到访人数', '到访人数占比'],
                    legendNameWidth: 80
                  }"
                />
              </CardSlot>
            </div>
            <div class="chart-item minor-children-chart">
              <div class="chart-item-title">
                <img src="@/assets/icon.png" alt="chart-title-icon" class="chart-title-icon" />
                <span class="chart-title-text">{{ minorChildrenChartTitle }}</span>
              </div>
              <CardSlot card-type="purple" :width="420" :height="370">
                <ConcentricPieChart
                  v-if="minorChildrenChartData.length > 0"
                  :data="minorChildrenChartData"
                  :config="{
                    width: 420,
                    height: 370,
                    legendItemGap: 30,
                    colors: ['#53FEFF', '#3B82F6'],
                    radius: ['15%', '55%'],
                    legendDistance: 25,
                    tipTypeName: ['到访人数', '到访人数占比'],
                    legendNameWidth: 100
                  }"
                />
              </CardSlot>
            </div>
            <div class="chart-item minor-children-age-chart">
              <div class="chart-item-title">
                <img src="@/assets/icon.png" alt="chart-title-icon" class="chart-title-icon" />
                <span class="chart-title-text">{{ minorChildrenAgeChartTitle }}</span>
              </div>
              <CardSlot card-type="purple" :width="420" :height="370">
                <ConcentricPieChart
                  v-if="minorChildrenAgeChartData.length > 0"
                  :data="minorChildrenAgeChartData"
                  :config="{
                    width: 420,
                    height: 370,
                    colors: ['#AFEC53', '#53FEFF', '#BB7AF9', '#489BFF'],
                    radius: ['15%', '55%'],
                    legendDistance: 25,
                    tipTypeName: ['到访人数', '到访人数占比'],
                    legendNameWidth: 80
                  }"
                />
              </CardSlot>
            </div>
          </div>
        </div>
      </div>
    </div>
  </layout-main>
</template>

<script lang="ts" setup>
import { BusinessDistrictEnum } from "@/views/report/components/enum";
import BusinessDistrictTags from "@/views/report/components/business-district-tags.vue";
import CardSlot from "@/views/report/components/card-slot.vue";
import ConcentricPieChart from "@/views/report/components/concentric-pie-chart.vue";
import { SLIDE_COMPONENTS_INDEX } from "@/views/report/config";
import { useSlideHook } from "../../hooks/useSlideHook";
import chartDataJson from "@/data/visitor-user-profile/客群婚姻与生育占比分析.json";

const currentDistrict = ref<string>(BusinessDistrictEnum["文化博览中心"]);

// 图表标题和副标题
const chartTitle = ref("客群婚育分布分析");
const chartTitleDesc = ref("");

const marriageChartTitle = ref("客群婚姻占比分布");
const marriageChartData = ref<any>([]);
const minorChildrenChartTitle = ref("客群是否有未成年子女");
const minorChildrenChartData = ref<any>([]);
const minorChildrenAgeChartTitle = ref("客群未成年子女年龄分布");
const minorChildrenAgeChartData = ref<any>([]);

// 预处理的商圈数据，包含所有商圈的数据
const processedDistrictData = ref<Record<string, any>>({});

// X轴数据（时间）- 从JSON数据中动态获取
const xAxisData = ref<string[]>([]);

// 更新图表数据
const updateChartData = () => {
  const currentData = processedDistrictData.value[currentDistrict.value];
  if (currentData) {
    marriageChartData.value = currentData.marriage;
    minorChildrenChartData.value = currentData.hasChildren;
    minorChildrenAgeChartData.value = currentData.childrenAge;
  }
};

// 清理图表数据
const clearChartData = () => {
  marriageChartData.value = [];
  minorChildrenChartData.value = [];
  minorChildrenAgeChartData.value = [];
};

const initData = () => {
  // 预处理所有商圈的数据，避免重复处理
  const bdsList = chartDataJson.result.bds_list;
  chartTitleDesc.value = chartDataJson.result.page_conclusion;

  bdsList.forEach((bds) => {
    const districtName = bds.bds_name;

    // 处理婚姻状况数据
    const marriageData = bds.marriage_list.map((item: any) => ({
      name: item.sec_name,
      value: item.sec_value,
      percentage: item.percentage
    }));

    // 处理是否有未成年子女数据
    const hasChildrenData = bds.child_list.map((item: any) => ({
      name: item.sec_name,
      value: item.sec_value,
      percentage: item.sec_percentage
    }));

    // 处理未成年子女年龄分布数据
    const childrenAgeData = bds.child_age_list.map((item: any) => ({
      name: item.sec_name,
      value: item.sec_value,
      percentage: item.sec_percentage
    }));

    // 存储处理后的数据
    processedDistrictData.value[districtName] = {
      marriage: marriageData,
      hasChildren: hasChildrenData,
      childrenAge: childrenAgeData
    };
  });
};

const handleTagChange = (tag: string) => {
  // 切换商圈时，先清理数据，再更新数据
  clearChartData();
  currentDistrict.value = tag;
  // 切换商圈时直接使用预处理的数据
  nextTick(() => {
    updateChartData();
  });
};

const handleClick = () => {
  document.documentElement.requestFullscreen();
};

useSlideHook((slide) => {
  if (slide === SLIDE_COMPONENTS_INDEX["marriageFertility"]) {
    initData();
    nextTick(() => {
      updateChartData();
    });
    updateChartData();
  } else {
    // 幻灯片离开时清理数据
    clearChartData();
  }
});

// 组件挂载时初始化数据
onMounted(() => {
  initData();
});
</script>

<style lang="scss" scoped>
.bg-img {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url("@/assets/visitor-group-bg.png") no-repeat;
  background-size: 100% 100%;
  background-position: center;
  filter: brightness(0.4);
}
.chart-container {
  width: 100%;
  height: calc(100% - 180px);
  padding-top: 60px;
  position: relative;
}
.title-container {
  display: flex;
  flex-direction: column;
  align-items: center;

  .title-desc-container {
    max-width: 710px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }

  .title-text {
    font-size: 22px;
    font-weight: 700;
    color: #b1fff2;
  }
  .title-desc-text {
    margin-top: 14px;
    font-size: 14px;
    font-weight: 400;
    color: #fff;
  }
}
.chart-box {
  width: 100%;
  height: calc(100% - 90px);
  padding: 20px 5% 0;

  .chart-layout {
    margin-top: 3.5%;

    .chart-item {
      .chart-item-title {
        margin-bottom: 35px;
        display: flex;
        align-items: center;

        .chart-title-icon {
          width: 18px;
          height: 23px;
          margin-right: 10px;
        }
        .chart-title-text {
          font-size: 16px;
          font-weight: 700;
          color: #f7fffe;
          text-shadow: 0px 4px 6px #4d429e;
        }
      }
    }
  }
}
</style>
