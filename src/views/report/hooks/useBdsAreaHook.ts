import districtBaseData from "@/data/business-district-overview/商圈首页.json";
import { BdsBaseData } from "@/views/report/type";
import { cloneDeep } from "lodash-es";
const TMap = (window as any).TMap;

interface AREA_OTPOSITE_TYPE {
  sortable?: boolean; // 是否排序，data排序先排好,下标越小，排名越高，围栏会有颜色，
  clickable?: boolean; // 是否可点击
  zIndex?: number;
}

// 商圈围栏
export const useBdsAreaHook = (
  mapInstance: any,
  data?: BdsBaseData[],
  options?: AREA_OTPOSITE_TYPE,
  areaClick?: (id: string) => void
) => {
  const { bds_list = [] } = districtBaseData.result;
  const bdsAreaData = bds_list.map((it) => {
    return {
      bds_id: it.bds_id,
      bds_name: it.bds_name,
      boundary: it.boundary
    };
  });

  const multiPolygon = new TMap.MultiPolygon({
    id: "multiPolygon",
    map: mapInstance,
    styles: {
      topOne: new TMap.PolygonStyle({
        color: "rgba(80, 244, 245, 0.45)", // 面填充色
        showBorder: true, // 是否显示拔起面的边线
        borderColor: "#C3FFF6", // 边线颜色
        borderDashArray: [3, 3], // 边线样式
        borderWidth: 2 // 边线宽度
      }),
      topTwo: new TMap.PolygonStyle({
        color: "rgba(242, 251, 180, 0.45)", // 面填充色
        showBorder: true, // 是否显示拔起面的边线
        borderColor: "#E8FF38", // 边线颜色
        borderDashArray: [3, 3], // 边线样式
        borderWidth: 2 // 边线宽度
      }),
      topThree: new TMap.PolygonStyle({
        color: "rgba(249, 183, 255, 0.3)", // 面填充色
        showBorder: true, // 是否显示拔起面的边线
        borderColor: "#B647FF", // 边线颜色
        borderDashArray: [3, 3], // 边线样式
        borderWidth: 2 // 边线宽度
      }),
      normalPloygon: new TMap.PolygonStyle({
        color: "rgba(184, 255, 199, 0.11)", // 面填充色
        showBorder: true, // 是否显示拔起面的边线
        borderColor: "#B8FFC7", // 边线颜色
        borderDashArray: [3, 3], // 边线样式
        borderWidth: 2 // 边线宽度
      })
    }
  });
  // 如果不传data， 默认展示所有商圈的围栏
  const validData = data
    ? data
    : bds_list.map((it) => {
        return {
          bds_id: it.bds_id
        };
      });

  const polygons = validData.map((item: BdsBaseData, index: number) => {
    const curr = bdsAreaData.find((it) => it.bds_id === item.bds_id);
    if (!curr) {
      return null;
    }
    const path = curr.boundary
      .split(";")
      .map((poi: any) => {
        const latLng = poi.split(",");
        return poi
          ? {
              lat: Number(latLng[1]),
              lng: Number(latLng[0]),
              height: 0
            }
          : null;
      })
      .filter((item: any) => item);

    let styleId: string = "normalPloygon";

    if (options?.sortable && data?.length) {
      // 判断当前商圈排名
      const styleMaps: { [key: number]: string } = {
        0: "topOne",
        1: "topTwo",
        2: "topThree"
      };

      styleId = styleMaps[index] ?? "normalPloygon";
    }

    return {
      id: item.bds_id,
      styleId: styleId, // 样式id
      paths: path // 多边形的位置信息
    };
  });
  multiPolygon.setGeometries(polygons);
  // 绑定事件
  if (options?.clickable) {
    // 高亮选中图层
    const highlightLayer = new TMap.MultiPolyline({
      map: mapInstance,
      id: "highlightLayer",
      zIndex: 1,
      styles: {
        highlight: new TMap.PolylineStyle({
          color: "#19FFDC", //填充色
          width: 2, // 线宽
          borderColor: "#fff", //边线颜色
          borderWidth: 1 //边线宽度
        })
      }
    });
    multiPolygon.on("click", (e: any) => {
      // 展示数据
      if (e.geometry) {
        areaClick && areaClick(e.geometry.id);
      }
    });
    // 高亮处理
    multiPolygon.on("hover", (e: any) => {
      if (e.geometry) {
        let paths = cloneDeep(e.geometry.paths);
        paths.push(paths[0]);
        highlightLayer.setGeometries([
          {
            styleId: "highlight",
            paths: paths
          }
        ]);
      } else {
        highlightLayer.setGeometries([]);
      }
    });
  }
};
