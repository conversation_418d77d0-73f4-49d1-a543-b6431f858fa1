import { ref, type Ref } from "vue";
import type { BdsBaseData } from "@/views/report/type";

// 类型定义
interface AreaDataItem {
  bds_id: string;
  name: string;
  position: {
    lat: number;
    lng: number;
    height?: number;
  };
  icon?: string;
  popupData?: {
    station_name: string;
    station_flow?: number;
    local_visitor_count?: number;
    non_local_visitor_count?: number;
  };
}

export interface BubbleData {
  bds_id: string;
  value: string;
  size: number;
  position: {
    lat: number;
    lng: number;
  };
}

export const useBdsMarkerHook = () => {
  const multiMarker: Ref<any> = ref(null);
  const markerPopups: Record<string, any> = {};

  // 标记点创建逻辑（使用 MultiMarker 优化性能）
  const createMultiMarkers = (
    instance: any,
    areaData: AreaDataItem[],
    onMarkerClick: (id: string) => void
  ) => {
    const TMap = (window as any).TMap;
    const styleMap: Record<string, any> = {};
    const defaultIcon = areaData[0]?.icon || "";

    // 样式预加载（避免重复创建）
    styleMap["default"] = new TMap.MarkerStyle({
      width: 32,
      height: 80,
      anchor: { x: 15, y: 50 },
      src: defaultIcon,
      zIndex: 2
    });

    areaData.forEach((item: AreaDataItem) => {
      if (item.icon && !styleMap[item.icon]) {
        styleMap[item.icon] = new TMap.MarkerStyle({
          width: 32,
          height: 80,
          anchor: { x: 15, y: 50 },
          src: item.icon,
          zIndex: 2
        });
      }
    });

    // 标记点数据批量构建
    const geometries = areaData.map((item: AreaDataItem) => ({
      id: item.bds_id,
      position: new TMap.LatLng(item.position.lat, item.position.lng, item.position.height || 0),
      styleId: item.icon ? item.icon : "default"
    }));

    multiMarker.value = new TMap.MultiMarker({
      map: instance,
      styles: styleMap,
      geometries
    });

    // 点击事件统一处理
    multiMarker.value.on("click", (e: any) => {
      const { id } = e.geometry;
      onMarkerClick(id);
      openMarkerPopup(id);
    });
  };

  // 弹窗创建逻辑（自动适配不同数据类型）
  const createMarkerPopups = (instance: any, areaData: AreaDataItem[]) => {
    const TMap = (window as any).TMap;
    
    areaData.forEach((item: AreaDataItem) => {
      if (!item.popupData?.station_name) return;

      const { station_name, station_flow, local_visitor_count, non_local_visitor_count } =
        item.popupData;
      const hasFlowData = station_flow !== undefined;
      const hasResidenceData =
        local_visitor_count !== undefined && non_local_visitor_count !== undefined;

      // 智能判断弹窗内容（客流/户籍分布）
      let popupContent = "";
      if (hasResidenceData) {
        popupContent = `
          <div class="v-marker-popup">
            <p class="popup-text">${station_name}</p>
            <p class="popup-text">本地客流：${local_visitor_count} 万</p>
            <p class="popup-text">外地客流：${non_local_visitor_count} 万</p>
          </div>
        `;
      } else if (hasFlowData) {
        popupContent = `
          <div class="v-marker-popup">
            <p class="popup-text">${station_name}</p>
            <p class="popup-text">客流人数：${station_flow} 万</p>
          </div>
        `;
      }

      if (!popupContent) return;

      const popup = new TMap.InfoWindow({
        map: instance,
        id: item.bds_id,
        position: new TMap.LatLng(item.position.lat, item.position.lng, item.position.height || 0),
        enableCustom: true,
        content: popupContent,
        zIndex: 100,
        offset: { x: 0, y: -60 }
      });

      popup.close();
      markerPopups[item.bds_id] = popup;
    });
  };

  // 弹窗交互逻辑
  const openMarkerPopup = (id: string) => {
    Object.keys(markerPopups).forEach((key) => {
      markerPopups[key][key === id ? "open" : "close"]();
    });
  };

  // 气泡创建逻辑
  const createBubbleWindows = (instance: any, data: BubbleData[] = []) => {
    const TMap = (window as any).TMap;
    
    data.forEach((item: BubbleData, index: number) => {
      const themeClass = ["top-one", "top-two", "top-three"][index] ?? "";
      const infoContent = `
        <div class="v-bds-bubble-window ${themeClass}" style="width: ${item.size}px; height: ${item.size}px;"> 
          <p class="text-center">${item.value}</p>
        </div>
      `;

      new TMap.InfoWindow({
        map: instance,
        id: item.bds_id,
        position: new TMap.LatLng(item.position.lat, item.position.lng),
        enableCustom: true,
        content: infoContent,
        zIndex: 1,
        offset: { x: 0, y: -24 }
      }).open();
    });
  };

  // 清理资源
  const destroyMarkers = () => {
    Object.values(markerPopups).forEach((popup) => popup.destroy());
    if (multiMarker.value) {
      multiMarker.value.destroy();
      multiMarker.value = null;
    }
  };

  return {
    multiMarker,
    markerPopups,
    createMultiMarkers,
    createMarkerPopups,
    openMarkerPopup,
    createBubbleWindows,
    destroyMarkers
  };
};
