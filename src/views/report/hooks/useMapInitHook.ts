interface MapInitOptions {
  container: HTMLElement;
  options: Record<string, any>;
}

class CreateMapInstance {
  private mapSDKLoaded: boolean = false;
  private container: HTMLElement;
  private options: Record<string, any>;

  constructor(opts: MapInitOptions) {
    this.container = opts.container;
    this.options = opts.options ?? {};
    this.createSourceMap();
  }

  private loadSDK() {
    return new Promise((resolve, reject) => {
      if (this.mapSDKLoaded) {
        resolve(true);
      }
      const script = document.createElement("script");
      script.type = "text/javascript";
      script.src =
        "https://map.qq.com/api/gljs?v=1.exp&libraries=geometry,visualization&key=DFBBZ-5IAWL-S2LPZ-EVAQJ-WCMGO-62FR3";
      script.onerror = reject;
      script.onload = () => {
        this.mapSDKLoaded = true;
        resolve(true);
      };
      document.head.appendChild(script);
    });
  }

  // 初始化地图实例
  private async createSourceMap() {
    if (!this.container) {
      console.error("地图容器不存在！");
      return false;
    }

    await this.loadSDK();

    return new Promise(async (resolve, reject) => {
      const TMap = (window as any).TMap;
      let mapLoaded = false;

      try {
        const instance = await new TMap.Map(this.container, {
          zoom: 14.5,
          viewMode: "3D",
          pitch: 45,
          rotation: -20,
          mapStyleId: "style1",
          zIndex: 10,
          minZoom: 13,
          maxZoom: 18,
          center: new TMap.LatLng(31.31331, 120.69778),
          ...this.options,
          baseMap: {
            type: "vector",
            features: ["base", "building3d", "label"],
            buildingRange: [14.5, 23]
          },
          renderOptions: {
            fogOptions: {
              color: "rgba(11, 27, 36, 0.1)"
            }
          }
        });

        // 移除默认控件（链式调用优化可读性）
        [
          TMap.constants.DEFAULT_CONTROL_ID.ZOOM,
          TMap.constants.DEFAULT_CONTROL_ID.SCALE,
          TMap.constants.DEFAULT_CONTROL_ID.ROTATION
        ].forEach((control) => {
          instance.removeControl(control);
        });

        // 地图加载完成监听
        instance.on("tilesloaded", () => {
          if (!mapLoaded) {
            console.log(`地图实例加载完成！`);
            mapLoaded = true;
            resolve(instance);
          }
        });
      } catch (error) {
        console.error("地图实例加载失败！", error);
        reject(false);
      }
    });
  }
}

export const useMapInitHook = (container: HTMLElement, options: Record<string, any>) => {
  return new CreateMapInstance({ container, options });
};
