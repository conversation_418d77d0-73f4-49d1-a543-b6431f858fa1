interface BdsTitleOptions {
  map: any;
  position: any;
  content: string;
  fontSize?: number;
  sort: number;
}

const bdsIcons: { [key: string]: string } = {
  0: new URL("@/assets/cyan_build_icon.png", import.meta.url).href,
  1: new URL("@/assets/yellow_build_icon.png", import.meta.url).href,
  2: new URL("@/assets/purple_build_icon.png", import.meta.url).href,
  normal: new URL("@/assets/green_build_icon.png", import.meta.url).href
};
const themeColor: { [key: string]: string } = {
  0: "#50F4F5",
  1: "#F4FFA2",
  2: "#b647ff",
  normal: "#A8FAB7"
};

export const useBdsTitleHook = (options: BdsTitleOptions) => {
  class BdsTitle extends (window as any).TMap.DOMOverlay {
    constructor(opts: BdsTitleOptions) {
      super(opts);
      this.position = opts.position;
      this.content = opts.content;
      this.fontSize = opts.fontSize ?? 13;
      this.sort = opts.sort; // 排名， 0: 绿色， 1: 黄色， 2: 紫色， normal: 蓝色
    }

    onInit(opts: BdsTitleOptions) {
      this.position = opts.position;
      this.content = opts.content;
      this.color = themeColor[opts.sort] ?? themeColor.normal;
      this.fontSize = opts.fontSize;
      this.sort = opts.sort;
    }

    createDOM() {
      const titleEle = document.createElement("div");
      titleEle.style.position = "absolute";
      titleEle.style.top = "0px";
      titleEle.style.left = "0px";
      titleEle.style.height = "24px";
      titleEle.style.padding = "0px 10px 0px 8px";
      titleEle.style.lineHeight = "24px";
      titleEle.style.textAlign = "center";
      titleEle.style.whiteSpace = "nowrap";
      titleEle.style.zIndex = "1";
      titleEle.style.borderRadius = "12px";
      titleEle.style.pointerEvents = "none";
      titleEle.style.fontSize = `${this.fontSize}px`;
      titleEle.style.color = this.color;
      titleEle.style.backgroundColor = "rgba(0, 0, 0, 0.6)";
      titleEle.style.border = `1px solid ${this.color}`;
      titleEle.innerHTML = this.content;
      // 图标
      const img = document.createElement("img");
      img.src = bdsIcons[this.sort] ?? bdsIcons.normal;
      img.style.width = "16px";
      img.style.height = "16px";
      img.style.marginRight = "2px";
      img.style.verticalAlign = "middle";
      titleEle.prepend(img);
      return titleEle;
    }

    // 更新DOM元素
    updateDOM() {
      if (!this.map) return;
      // 经纬度坐标转容器像素坐标
      const pixel = this.map.projectToContainer(this.position);
      // 使饼图中心点对齐经纬度坐标点
      const left = `${pixel.getX() - this.dom.clientWidth / 2}px`;
      const top = `${pixel.getY()}px`;
      this.dom.style.transform = `translate3d(${left}, ${top}, 0)`;
    }

    onDestroy() {}
  }

  return new BdsTitle(options);
};
