<template>
  <div ref="chartRef" class="chart" :style="{ width: width, height: height }"></div>
</template>
<script setup lang="ts">
import * as echarts from "echarts";

// 定义组件属性
interface Props {
  // 图表尺寸
  width?: string;
  height?: string;
  // 柱形图数据
  barData?: {
    name: string;
    value: number;
    count?: number;
  }[];
  // 折线图数据
  lineData?: {
    name: string;
    value: number;
  }[];
  // 柱形图颜色配置
  barColors?: string[];
  // 折线图颜色配置
  lineColors?: string[];
  // 是否显示3D效果
  enable3D?: boolean;
  // 3D柱形图颜色配置
  bar3DColors?: {
    left: string;
    right: string;
    topFill: string;
  };
  // 自定义配置选项
  customOptions?: echarts.EChartsOption;
  // 图表标题
  title?: string;
  // 是否显示图例
  showLegend?: boolean;
  // 是否显示网格线
  showGrid?: boolean;
  // 是否启用动画
  enableAnimation?: boolean;
  // 柱形图名称
  barDataName?: string;
  // 折线图名称
  lineDataName?: string;
  // 提示词
  tipTypeName?: string[];
  // 是否显示十万单位
  isTenThousandUnits?: boolean;
  // 额外名称
  beforeName?: string;
  afterName?: string;
  // 值颜色索引
  valueColorIndex?: number;
  // 渐变色
  gradientColors?: {
    top: string;
    bottom: string;
  };
  // y轴左边名称的padding
  yLeftNamePadding?: number[];
  // y轴右边名称的padding
  yRightNamePadding?: number[];
  // tip单位
  tipUnit?: string;
  // percentage 字段
  percentageField?: string;
  // 折线图是否平滑
  lineSmooth?: boolean;
  // 折线颜色
  lineColor?: string;
  // 折线宽度
  lineWidth?: number;
}

const props = withDefaults(defineProps<Props>(), {
  width: '100%',
  height: '100%',
  barColors: () => ['#3B82F6', '#53FEFF', '#BB7AF9', '#F59E0B', '#EF4444'],
  bar3DColors: () => ({
    left: 'rgba(80, 205, 255, 0.8)',
    right: 'rgba(80, 205, 255, 1)',
    topFill: '#00BEFE',
  }), // 左 右 顶
  lineColors: () => ['#FCD34D', '#10B981', '#8B5CF6', '#F97316', '#EC4899'],
  enable3D: true,
  showLegend: true,
  showGrid: false,
  enableAnimation: true,
  barDataName: '%',
  lineDataName: '%',
  tipTypeName: () => ['', ''],
  isTenThousandUnits: true,
  beforeName: '',
  afterName: '',
  valueColorIndex: 0,
  gradientColors: () => ({
    top: 'rgba(18, 131, 255, 0)',
    bottom: 'rgba(25, 36, 92, 0.8)'
  }),
  yLeftNamePadding: () => [0, 0, 10, -20],
  yRightNamePadding: () => [0, 0, 10, 20],
  tipUnit: '万',
  percentageField: 'value',
  lineSmooth: false,
  lineColor: '#FCD34D',
  lineWidth: 2
});

const chartRef = ref<HTMLDivElement | null>(null);
let myChart: echarts.ECharts | null = null;
let resizeTimer: number | null = null;

// 默认配置选项
const getDefaultOptions = (): echarts.EChartsOption => {
  const hasLineData = props.lineData && props.lineData.length > 0;
  // 计算柱形图数据的最大值，并加5
  // 取出柱形图数据的 value 字段
  const barDataValues = props.barData?.map((item: any) => item.value) || [];
  const barDataCounts = props.barData?.map((item: any) => item.count || 0) || [];
  const maxBarValue = barDataValues.length > 0 ? Math.max(...barDataValues) : 0;
  const highBarValue = maxBarValue + 5;
  const filledArray = Array(barDataValues.length).fill(highBarValue > 100 ? 100 : highBarValue);
  // 计算柱条宽度和渐变色彩
  const barWidth = 20;
  const colors: any[] = [];
  for(let i = 0; i < 4; i++){
    colors.push({
      type: 'linear',
      x: 0,
      x2: 1,
      y: 0,
      y2: 0,
      colorStops: [
        {
          offset: 0,
          color: props.bar3DColors.left // 最左边
        }, {
          offset: 0.5,
          color: props.bar3DColors.left // 左边的右边 颜色
        }, {
          offset: 0.5,
          color: props.bar3DColors.right // 右边的左边 颜色
        }, {
          offset: 1,
          color: props.bar3DColors.right
        }]
    });
  }
  
  return {
    title: {
      text: props.title || '',
      show: !!props.title,
      textStyle: {
        color: '#FFFFFF',
        fontSize: 16,
        fontWeight: 'bold'
      },
      left: 'center',
      top: 10
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      backgroundColor: 'rgba(31, 26, 34, 0.9)',
      borderColor: "rgba(0,0,0,0)",
      borderWidth: 1,
      textStyle: {
        color: '#fff'
      },
      formatter: (paramsData: any) => {
        const params = paramsData[0];
        const count = barDataCounts[params.dataIndex];
        const formatValue = props.isTenThousandUnits ? (count / 10000).toFixed(2) : count;
        const currBarData = props.barData?.[params.dataIndex] || {} as any;
        const percentageValue:number | undefined = props.percentageField === 'value' ? params.value : currBarData[props.percentageField] || undefined;
        return `<div style="display: flex; flex-direction: column; padding: 2px;">
          <div style="font-size: 12px; color: #fff;">${props.beforeName ? `${props.beforeName}` : ""}${params.name}${props.afterName ? `${props.afterName}` : ""}</div>
          <div style="margin: 6px 0;font-size: 12px; display: flex; justify-content: space-between; align-items: center;">
            <span style="color: #E2F6FE; padding-right: 20px;">${props.tipTypeName[0]}</span>
            <span style="color: ${props.barColors[props.valueColorIndex]}; font-size: 12px; font-weight: 600;">${formatValue}${props.isTenThousandUnits ? props.tipUnit : ""}</span>
          </div>
          <div style="font-size: 12px; display: flex; justify-content: space-between; align-items: center;">
            <span style="color: #E2F6FE; padding-right: 20px;">${props.tipTypeName[1]}</span>
            <span style="color: ${props.barColors[props.valueColorIndex]}; font-size: 12px; font-weight: 600;">${percentageValue}%</span>
          </div>
        </div>`;
      }
    },
    legend: {
      show: props.showLegend,
      orient: 'horizontal',
      itemWidth: 12,
      itemHeight: 12,
      itemGap: 20,
      right: 20,
      top: props.title ? 50 : 20,
      icon: 'circle',
      textStyle: {
        fontSize: 12,
        color: '#fff'
      }
    },
    grid: {
      left: '5%',
      right: '5%',
      bottom: '3%',
      top: props.title ? '20%' : '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: props.barData?.map(item => item.name) || [],
      axisLabel: {
        color: '#FFFFFF',
        fontSize: 12,
        interval: 0,
        margin: 10,
        align: 'center'
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: '#BDC6D8'
        }
      },
      axisTick: {
        show: false
      }
    },
    yAxis: [
      {
        type: 'value',
        name: props.barDataName,
        nameTextStyle: {
          color: '#8490A9',
          fontWeight: 700,
          fontSize: 12,
          align: 'left',
          padding: props.yLeftNamePadding
        },
        axisLabel: {
          color: '#8490A9',
          fontWeight: 700,
          fontSize: 12,
          formatter: function(value: number) {
            return props.barDataName === '万元' ? `${(value / 10000).toFixed(2)}万` : value;
          }
        },
        axisLine: {
          show: false
        },
        splitLine: {
          show: props.showGrid,
          lineStyle: {
            type: 'dashed',
            color: '#BDC6D8',
            width: 1,
            opacity: 0.3
          }
        }
      },
      ...(hasLineData ? [{
        type: 'value',
        name: props.lineDataName,
        nameTextStyle: {
          color: '#8490A9',
          fontWeight: 700,
          fontSize: 12,
          padding: props.yRightNamePadding
        },
        min: 0,
        max: 100,
        axisLabel: {
          color: '#BDC6D8',
          fontSize: 12,
          formatter: '{value}%'
        },
        axisLine: {
          show: false
        },
        splitLine: {
          show: false
        }
      }]:[] as any[])
    ],
    series: [
      {
        name: '柱形图',
        type: 'bar' as const,
        data: props.barData?.map((item: any) => item.value) || [],
        itemStyle: {
          color: function(params: any) {
              return colors[params.dataIndex % 4];
          }
        },
        barWidth: barWidth,
        emphasis: {
          itemStyle: {
            shadowBlur: 0,
            shadowColor: 'transparent'
          }
        },
        animation: props.enableAnimation,
        animationDuration: 1000,
        animationEasing: 'cubicOut'
      },
      {
        z: 3,
        type: 'pictorialBar',
        data: props.barData?.map(item => item.value / 2) || [],
        symbol: 'rect',
        symbolSize: [barWidth, '100%'],
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: props.gradientColors.top,
              },
              {
                offset: 1,
                color: props.gradientColors.bottom,
              },
            ]),
        },
        label: {
          show: false,
        },
        animation: false,
      },
      {
        z: 3,
        type: 'pictorialBar',
        symbolPosition: 'end',
        data: props.barData?.map(item => item.value) || [],
        symbol: 'diamond',
        symbolOffset: [0, '-50%'],
        symbolSize: [barWidth, barWidth * 0.5],
        itemStyle: {
          borderWidth: 0,
          color: props.bar3DColors.topFill,
        },
      },
      {
        z: 3,
        type: 'pictorialBar',
        data: filledArray,
        symbol: 'rect',
        symbolSize: [barWidth, '100%'],
        itemStyle: {
          color: "transparent",
        },
        label: {
          show: false,
        },
        animation: false,
      },
      // 折线图系列（如果有数据）
      ...(hasLineData ? [{
        name: '折线图',
        type: 'line',
        yAxisIndex: 1,
        data: props.lineData?.map(item => item.value) || [],
        symbol: 'circle',
        symbolSize: 8,
        lineStyle: {
          color: props.lineColor,
          width: props.lineWidth
        },
        itemStyle: {
          color: props.lineColor,
          borderWidth: 1,
          borderColor: props.lineColor
        },
        smooth: props.lineSmooth,
      }] : [])
    ] as any[]
  };
};

// 合并自定义配置
const mergeOptions = (defaultOptions: echarts.EChartsOption, customOptions?: echarts.EChartsOption): echarts.EChartsOption => {
  if (!customOptions) return defaultOptions;
  
  // 深度合并配置
  const mergeSeries = (defaultSeries: any[], customSeries: any[]) => {
    if (!customSeries || !Array.isArray(customSeries)) return defaultSeries;
    
    return customSeries.map((customItem, index) => {
      const defaultItem = defaultSeries[index];
      if (defaultItem) {
        return { ...defaultItem, ...customItem };
      }
      return customItem;
    });
  };
  const mergedOptions = { ...defaultOptions, ...customOptions };
  
  // 特殊处理 series 的合并
  if (customOptions.series && defaultOptions.series) {
    mergedOptions.series = mergeSeries(defaultOptions.series as any[], customOptions.series as any[]);
  }
  
  return mergedOptions;
};

// 创建图表配置
const createChartOption = (): echarts.EChartsOption => {
  const defaultOptions = getDefaultOptions();
  return mergeOptions(defaultOptions, props.customOptions);
};

// 更新图表
const updateChart = () => {
  if (!myChart || !chartRef.value) return;
  myChart.setOption(createChartOption(), { notMerge: true });
};

// 处理窗口大小变化
const handleResize = () => {
  if (resizeTimer) {
    window.clearTimeout(resizeTimer);
  }
  resizeTimer = window.setTimeout(() => myChart?.resize(), 100);
};

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return;
  myChart = echarts.init(chartRef.value, null, { renderer: 'svg' });
  updateChart();
};

// 监听属性变化
watch(
  () => [
    props.barData,
    props.lineData,
    props.barColors,
    props.lineColors,
    props.enable3D,
    props.customOptions,
    props.title,
    props.showLegend,
    props.showGrid,
    props.enableAnimation
  ],
  () => nextTick(updateChart),
  { deep: true }
);

// 监听尺寸变化
watch(
  () => [props.width, props.height],
  () => nextTick(() => {
    if (myChart) {
      myChart.resize();
    }
  })
);

onMounted(() => {
  window.addEventListener('resize', handleResize);
  nextTick(() => {
    initChart();
  });
});

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  if (resizeTimer) {
    window.clearTimeout(resizeTimer);
  }
  myChart?.dispose();
});

// 暴露方法
defineExpose({
  initChart,
  updateChart,
  resize: () => myChart?.resize(),
  getChart: () => myChart
});
</script>

<style scoped>
.chart {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}
</style>
