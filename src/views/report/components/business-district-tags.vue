<template>
  <div class="flex gap-6 px-2 py-3" style="pointer-events: auto;">
    <div
      v-for="(tag, index) in tags"
      :key="index"
      class="text-white fs-14 fw-400 cursor-pointer px-0.5 py-0.5 rounded-md transition-all duration-300 relative whitespace-nowrap select-none"
      :class="{ 
        'text-active fw-500': activeTag === tag.value 
      }"
      @click="handleTagClick(tag.value)"
    >
      {{ tag.label }}
      <div 
        v-if="activeTag === tag.value"
        class="absolute bottom-[-4px] left-1/2 transform -translate-x-1/2 w-4/5 h-0.5 rounded line-active"
      ></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { BusinessDistrictEnum } from './enum';

interface TagItem {
  label: string;
  value: string;
}

interface Props {
  defaultActive?: string;
}

const props = withDefaults(defineProps<Props>(), {
  defaultActive: BusinessDistrictEnum['星海广场']
});

const tags: TagItem[] = [
  { label: '文化博览中心', value: BusinessDistrictEnum['文化博览中心'] },
  { label: '时代广场', value: BusinessDistrictEnum['时代广场'] },
  { label: '圆融广场', value: BusinessDistrictEnum['圆融广场'] },
  { label: '星海广场', value: BusinessDistrictEnum['星海广场'] },
  { label: '苏州中心', value: BusinessDistrictEnum['苏州中心'] },
  { label: '李公堤', value: BusinessDistrictEnum['李公堤'] },
  { label: '奥体中心', value: BusinessDistrictEnum['奥体中心'] },
  { label: '阳澄', value: BusinessDistrictEnum['阳澄'] }
];

const activeTag = ref(props.defaultActive);

// 监听props变化，更新activeTag
watch(() => props.defaultActive, (newValue) => {
  activeTag.value = newValue;
});

const emit = defineEmits<{
  tagChange: [value: string];
}>();

const handleTagClick = (value: string) => {
  activeTag.value = value;
  emit('tagChange', value);
};
</script>

<style lang="scss" scoped>
.text-active {
  color: #C3FFF6;
}
.line-active {
  background-color: #93F4FF;
}
</style> 