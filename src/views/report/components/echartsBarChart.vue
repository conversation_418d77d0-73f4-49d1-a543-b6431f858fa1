<template>
  <div ref="chartRef" class="chart" style="width: 100%; height: 330px"></div>
</template>

<script setup lang="ts">
import * as echarts from "echarts";

// 定义 Props 并设置默认值，增强类型安全
const props = defineProps<{
  xAxisData?: string[];
  consumeData?: (number | string)[];
  visitData?: (number | string)[];
}>();

const chartRef = ref<HTMLDivElement | null>(null);
let myChart: echarts.ECharts | null = null;
let resizeTimer: number | null = null;

// 【优化1】默认数据语义化命名 + 类型明确
const DEFAULT_CHART_DATA = {
  xAxisData: [],
  consumeData: [],
  visitData: []
};

// 【优化2】数据预处理（独立函数，解耦复杂逻辑）
const processData = (): {
  xAxisData: string[];
  consumeDataNum: number[];
  visitDataNum: number[];
} => {
  const xAxisData = props.xAxisData || DEFAULT_CHART_DATA.xAxisData;
  const consumeDataNum = (props.consumeData || []).map(Number);
  const visitDataNum = (props.visitData || []).map(Number);

  // 兜底处理：防止数据为空导致图表异常
  return {
    xAxisData: xAxisData.length ? xAxisData : DEFAULT_CHART_DATA.xAxisData,
    consumeDataNum: consumeDataNum.length ? consumeDataNum : DEFAULT_CHART_DATA.consumeData,
    visitDataNum: visitDataNum.length ? visitDataNum : DEFAULT_CHART_DATA.visitData
  };
};

// 【优化3】重构配置项生成（纯函数化，增强可测试性）
const createChartOption = (): echarts.EChartsOption => {
  const { xAxisData, consumeDataNum, visitDataNum } = processData();

  // 计算非消费数据（提取为独立逻辑）
  const nonConsumeData = visitDataNum.map((visit, index) =>
    Math.max(0, visit - (consumeDataNum[index] || 0))
  );

  return {
    title: { show: false },
    tooltip: {
      trigger: "axis",
      axisPointer: { type: "shadow" },
      formatter: (params: echarts.EChartsTooltipCallbackParams) => {
        const name = params[0]?.name || "";
        const consumeValue = params[0]?.value || 0;
        const nonConsumeValue = params[1]?.value || 0;
        const visitValue = Number(consumeValue) + Number(nonConsumeValue);

        return `${name}商圈<br/>
                消费人次：<span style="color:#00FDFF">${consumeValue}%</span><br/>
                非消费人次：<span style="color:#D2D7EC">${nonConsumeValue}%</span><br/>
                到访总人次：<span style="color:#FFF">${visitValue}%</span>`;
      },
      backgroundColor: "rgba(0,0,0,0.7)",
      borderColor: "#000",
      textStyle: { color: "#fff" }
    },
    legend: {
      orient: "horizontal",
      itemWidth: 10,
      itemHeight: 10,
      itemGap: 15,
      right: 0,
      top: 0,
      icon: "circle",
      textStyle: { fontSize: 12, color: "#fff" }
    },
    grid: {
      left: "0",
      right: "0",
      bottom: "0",
      top: "14%",
      containLabel: true
    },
    xAxis: {
      type: "category",
      data: xAxisData,
      axisLabel: {
        color: "#FFFFFF",
        fontSize: 12,
        interval: 0,
        margin: 10,
        align: "center"
      },
      axisLine: { show: true },
      axisTick: { show: false }
    },
    yAxis: {
      type: "value",
      axisLabel: {
        formatter: "{value}%",
        color: "#BDC6D8"
      },
      axisLine: { show: false },
      splitLine: {
        show: true,
        lineStyle: {
          type: "dashed",
          color: "#BDC6D8",
          width: 1,
          opacity: 0.3
        }
      }
    },
    series: [
      {
        name: "消费人次",
        type: "bar",
        stack: "总量",
        data: consumeDataNum,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: "#00FFFF" },
            { offset: 1, color: "#00BCFF" }
          ]),
          borderRadius: [18, 18, 0, 0]
        },
        label: {
          show: false,
          position: "insideTop",
          color: "#fff",
          fontSize: 12,
          distance: 5
        },
        barWidth: 14
      },
      {
        name: "到访人次",
        type: "bar",
        stack: "总量",
        data: nonConsumeData,
        itemStyle: {
          color: "rgba(210, 215, 236, 0.16)"
        },
        label: {
          show: false,
          position: "insideBottom",
          color: "#fff",
          fontSize: 12,
          distance: 5
        },
        barWidth: 14
      }
    ]
  };
};

// 【优化4】统一更新逻辑（解耦初始化与更新）
const updateChart = () => {
  if (!myChart || !chartRef.value) return;
  myChart.setOption(createChartOption(), { notMerge: true });
};

// 【优化5】重构 resize 逻辑（清晰的防抖处理）
const handleResize = () => {
  resizeTimer && window.clearTimeout(resizeTimer);
  resizeTimer = window.setTimeout(() => myChart?.resize(), 100);
};

// 【优化6】修复暴露方法（新增 initChart 定义）
const initChart = () => {
  if (!chartRef.value) return;
  myChart = echarts.init(chartRef.value);
  updateChart();
};

// 监听 Props 变化（明确依赖，避免不必要触发）
watch(
  () => [props.xAxisData, props.consumeData, props.visitData],
  () => nextTick(updateChart),
  { deep: true }
);

onMounted(() => {
  window.addEventListener("resize", handleResize);
});

onUnmounted(() => {
  window.removeEventListener("resize", handleResize);
  resizeTimer && window.clearTimeout(resizeTimer);
  myChart?.dispose();
});

// 【优化7】明确暴露方法（修复原代码中 initChart 未定义问题）
defineExpose({
  initChart: () => initChart(),
  myChart: () => myChart
});
</script>

<style scoped>
/* 优化响应式体验：添加过渡动画 */
.chart {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}
</style>
