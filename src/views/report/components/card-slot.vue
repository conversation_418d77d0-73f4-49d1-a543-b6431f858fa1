<template>
  <div class="card-container" :style="{ width: width + 'px', height: height + 'px' }">
    <div class="card-bg" :class="cardType"></div>
    <div class="card-content">
      <slot></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  cardType?: 'cyan' | 'purple' // 控制背景图片类型 cyan 青色边框，purple 紫色边框
  width?: number
  height?: number
}

const props = withDefaults(defineProps<Props>(), {
  cardType: 'cyan',
  width: 470,
  height: 465
})
</script>

<style scoped lang="scss">
.card-container {
  box-sizing: border-box;
  position: relative;
  z-index: 11;
  min-height: 200px;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  pointer-events: none;
  
  .card-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(141deg, rgba(40, 41, 92, 0.003) 3%, rgba(17, 9, 39, 0.003) 106%);
    backdrop-filter: blur(20px);
    border-radius: 12px;

    &.cyan {
      background-image: url('/src/assets/cyan-border-bg.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }
    
    &.purple {
      background-image: url('/src/assets/purple-border-bg.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }
  }

  .card-content {
    height: 100%;
    position: relative;
    z-index: 1;
    pointer-events: auto;
  }
}
</style>
