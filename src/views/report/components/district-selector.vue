<template>
  <div class="district-selector-container">
    <div class="selector-wrapper">
      <div class="arrow-btn" :class="{ 'left-arrow': canScrollLeft }" @click="scrollLeft" v-show="canScrollLeft" >
        <el-icon :size="22"><ArrowLeft /></el-icon>
      </div>

      <div class="tags-container" ref="tagsContainer">
        <business-district-tags
          :default-active="activeDistrict"
          @tag-change="handleDistrictChange"
        />
      </div>

      <div class="arrow-btn" :class="{ 'right-arrow': canScrollRight }" @click="scrollRight" v-show="canScrollRight">
        <el-icon :size="22"><ArrowRight /></el-icon>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick, watch } from "vue";
import BusinessDistrictTags from "./business-district-tags.vue";
import { ArrowLeft, ArrowRight } from "@element-plus/icons-vue";

interface Props {
  defaultActive?: string;
}

const props = withDefaults(defineProps<Props>(), {
  defaultActive: "文化博览中心"
});

const emit = defineEmits<{
  districtChange: [district: string];
}>();

// 响应式数据
const activeDistrict = ref(props.defaultActive);
const tagsContainer = ref<HTMLElement>();
const canScrollLeft = ref(true);
const canScrollRight = ref(true);

// 处理商圈切换
const handleDistrictChange = (district: string) => {
  activeDistrict.value = district;
  emit('districtChange', district);
};

// 滚动控制
const scrollLeft = () => {
  if (tagsContainer.value) {
    tagsContainer.value.scrollBy({ left: -100, behavior: "smooth" });
  }
};

const scrollRight = () => {
  if (tagsContainer.value) {
    tagsContainer.value.scrollBy({ left: 100, behavior: "smooth" });
  }
};

// 检查滚动状态
const checkScrollStatus = () => {
  if (tagsContainer.value) {
    const { scrollLeft, scrollWidth, clientWidth } = tagsContainer.value;
    canScrollLeft.value = scrollLeft > 0;
    canScrollRight.value = scrollLeft < scrollWidth - clientWidth - 1;
    
    // 如果内容宽度小于容器宽度，隐藏箭头
    if (scrollWidth <= clientWidth) {
      canScrollLeft.value = false;
      canScrollRight.value = false;
    }
  }
};

// 监听props变化
watch(() => props.defaultActive, (newValue) => {
  activeDistrict.value = newValue;
});

// 监听滚动事件
onMounted(() => {
  nextTick(() => {
    if (tagsContainer.value) {
      tagsContainer.value.addEventListener("scroll", checkScrollStatus);
      checkScrollStatus();
    }
  });
});

onUnmounted(() => {
  if (tagsContainer.value) {
    tagsContainer.value.removeEventListener("scroll", checkScrollStatus);
  }
});
</script>

<style scoped lang="scss">
.district-selector-container {
  margin-bottom: 25px;
  pointer-events: auto;
}

.selector-wrapper {
  padding: 0;
  position: relative;
  display: flex;
  align-items: center;
}

.tags-container {
  width: calc(100% - 60px);
  flex: 1;
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;

  &::-webkit-scrollbar {
    display: none;
  }
}

.arrow-btn {
  display: flex;
  align-items: center;
  width: 28px;
  height: 28px;
  color: #93f4ff;
  transition: all 0.3s ease;
  flex-shrink: 0;

  &.left-arrow {
    justify-content: left;
    cursor: pointer;
  }

  &.right-arrow {
    justify-content: right;
    cursor: pointer;
  }
}
</style>
