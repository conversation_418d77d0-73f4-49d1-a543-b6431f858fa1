<template>
  <div class="v-layout-footer">
    <div
      class="v-layout-footer__content flex justify-between items-center content-stretch fs-14 br-8"
    >
      <div class="w-216 h-140 p-24" @click="handleFullScreen">
        <p class="text-center fs-18 c-primary">苏州工业园区</p>
      </div>
      <el-divider direction="vertical" style="height: 140px; opacity: 0.2" />
      <div class="flex-1 h-140 ptb-12 pl-30 pr-50 flex justify-between items-center">
        <div class="flex-col h-full" v-for="(item, index) in quickSlides" :key="index">
          <p class="text-center fs-15 c-primary mb-8">{{ item.title }}</p>
          <div class="flex-col justify-start">
            <span
              class="v-layout-footer__cell"
              :class="{ active: child.page_no === slideStore.currentSlide }"
              v-for="child in item.children"
              :key="child.title"
              @click="jumpSlide(child.page_no)"
            >
              {{ child.title }}
            </span>
          </div>
        </div>
      </div>
      <el-divider direction="vertical" style="height: 140px; opacity: 0.2" />
      <div class="w-152 p-16 h-140 flex-col justify-center items-center">
        <div class="flex-col justify-center items-center h-60 c-fff fs-16">
          <p>{{ showCurrentSlide }}</p>
          <p>— —</p>
          <p>{{ slideStore.totalSlideCount }}</p>
        </div>
        <div class="flex items-center">
          <el-image
            :src="IconLeft"
            alt="icon-left-circle"
            class="w-28 h-28 mr-4 cursor-pointer"
            @click.stop="changeSlide('prev')"
          ></el-image>
          <el-image
            :src="IconRight"
            alt="icon-right-circle"
            class="w-28 h-28 ml-4 cursor-pointer"
            @click.stop="changeSlide('next')"
          ></el-image>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import IconLeft from "@/assets/icons/icon-left-circle.png";
import IconRight from "@/assets/icons/icon-right-circle.png";
import { useSlideStore } from "@/store/modules/slide";
import { debounce } from "lodash-es";
import { FOOTER_LINK_SETTING } from "../config";
const slideStore = useSlideStore();

/**
 * 快速导航相关
 * page_no 代表页码,从 0 开始
 *  */
const quickSlides = ref(FOOTER_LINK_SETTING);

// 页码相关
const showCurrentSlide = ref(slideStore.currentSlide + 1);

watch(
  () => slideStore.currentSlide,
  (val) => {
    setCurSlide(val);
  }
);

const setCurSlide = debounce((val: number) => {
  showCurrentSlide.value = val + 1;
}, 150);

const jumpSlide = (num: number) => {
  slideStore.actionJumpToSlide(num);
};

const changeSlide = (direction: "prev" | "next") => {
  if (direction === "prev") {
    slideStore.actionPrevSlide();
  } else {
    slideStore.actionNextSlide();
  }
};

const handleFullScreen = () => {
  document.documentElement.requestFullscreen();
};
</script>

<style lang="scss" scoped>
.v-layout-footer {
  position: absolute;
  width: 100%;
  bottom: 10px;
  left: 0;
  background-color: transparent;
  padding: 0 12px;
  box-sizing: border-box;
  z-index: 1200;
  &__content {
    height: 145px;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
  }
  &__cell {
    display: inline-block;
    height: 18px;
    width: fit-content;
    line-height: 18px;
    padding: 0 8px;
    background-color: transparent;
    border-radius: 8px;
    font-size: 12px;
    margin-bottom: 2px;
    color: #fff;
    cursor: pointer;
    user-select: none;
    transition: all 0.3s ease;
    &.active {
      background-color: #c3fff6;
      color: #1a5c53;
      text-decoration: none;
    }
    &:hover {
      text-decoration: underline;
    }
  }
}
</style>
