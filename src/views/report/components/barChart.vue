
<template>
  <div ref="chartRef" class="chart" style="width: 100%; height: 440px"></div>
</template>
<script setup lang="ts">
import * as echarts from "echarts";
// 定义 Props 并设置默认值，匹配原图表数据维度
const props = defineProps<{
  xAxisData?: string[];
  profitData?: (number | string)[];
  expenditureData?: (number | string)[];
}>();
const chartRef = ref<HTMLDivElement | null>(null);
let myChart: echarts.ECharts | null = null;
let resizeTimer: number | null = null;
// 【优化1】默认数据语义化命名 + 类型明确（匹配原配置默认数据）
const DEFAULT_CHART_DATA = {
  xAxisData: [],
  profitData: [200, 170, 240, 244, 200, 220, 210, 250],
  expenditureData: [-120, -132, -101, -134, -190, -230, -210, -250]
};
// 【优化2】数据预处理（统一转为Number，兜底空数据）
const processData = (): {
  xAxisData: string[];
  profitDataNum: number[];
  expenditureDataNum: number[];
} => {
  const xAxisData = props.xAxisData || DEFAULT_CHART_DATA.xAxisData;
  const profitDataNum = (props.profitData || []).map(Number);
  const expenditureDataNum = (props.expenditureData || []).map(Number);
  // 兜底：确保数据长度与x轴一致，避免图表异常
  const fillDefault = <T>(data: T[], defaultData: T[]): T[] => (data.length ? data : defaultData);
  return {
    xAxisData: fillDefault(xAxisData, DEFAULT_CHART_DATA.xAxisData),
    profitDataNum: fillDefault(profitDataNum, DEFAULT_CHART_DATA.profitData),
    expenditureDataNum: fillDefault(expenditureDataNum, DEFAULT_CHART_DATA.expenditureData)
  };
};
// 【优化3】重构配置项生成（纯函数，保留原图表核心逻辑）
const createChartOption = (): echarts.EChartsOption => {
  const { xAxisData, profitDataNum, expenditureDataNum } = processData();
  return {
    title: { show: false },
    tooltip: {
      trigger: "axis",
      axisPointer: { type: "shadow" },
      backgroundColor: "#042c52",
      borderColor: "#042c52",
      textStyle: { color: "#fff" },
      formatter: function (params: echarts.EChartsTooltipCallbackParams) {
        // 假设 x 轴是“苏州中心”这类名称，取第一个点的 x 轴名称
        const title = params[0]?.name || "未知商圈";
        // 计算总和（依旧以人数场景示例）
        const total = params.reduce((acc, cur) => acc + Math.abs(cur.value), 0);
        let tooltipHtml = `
      <div style="padding: 12px; font-size: 14px;">
        <div style="font-weight: bold; margin-bottom: 8px;">${title}</div>
    `;
        params.forEach((item, index) => {
          const seriesName = item.seriesName;
          const value = item.value;
          const absValue = Math.abs(value);
          const ratio = total > 0 ? ((absValue / total) * 100).toFixed(2) : "0.00";
          // 模拟“1.2万”：如果数值大可以除以 10000 做单位转换
          const displayValue = absValue >= 10000 ? `${(absValue / 10000).toFixed(1)}万` : absValue;
          // 高亮占比、数值
          const lightStyle = "color: #ff88aa; font-weight: bold;";
          const highlightStyle = "color: #7E93F5; font-weight: bold;";

          tooltipHtml += `
        <div style="margin-bottom: 4px;">
          ${seriesName}性到访人数：<span style="${
            seriesName == "男" ? highlightStyle : lightStyle
          }">${displayValue}</span><br/>
          ${seriesName}性到访人数占比：<span style="${
            seriesName == "男" ? highlightStyle : lightStyle
          }">${ratio}%</span>
        </div>
      `;
        });
        tooltipHtml += `</div>`;
        return tooltipHtml;
      }
    },
    legend: {
      orient: "horizontal",
      itemWidth: 10,
      itemHeight: 10,
      itemGap: 15,
      right: "5%",
      top: 0,
      icon: "circle",
      textStyle: { fontSize: 12, color: "#fff" }
    },
    grid: {
      left: "5%",
      right: "5%",
      bottom: "0",
      top: "14%",
      containLabel: true
    },
    xAxis: [
      {
        type: "category",
        axisTick: { show: false }, // 隐藏x轴刻度线
        data: xAxisData,
        axisLabel: {
          color: "#FFFFFF",
          fontSize: 12,
          interval: 0,
          margin: 10,
          align: "center"
        },
        axisLine: { show: true }
      }
    ],
    yAxis: [
      {
        show: false, // 隐藏y轴
        type: "value",
        axisLabel: { show: false }
      }
    ],
    series: [
      // 利润（堆叠总量1）
      {
        name: "女",
        type: "bar",
        stack: "总量1",
        data: profitDataNum,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: "rgba(255, 155, 185, 1)" },
            { offset: 1, color: "rgba(255, 155, 185, 0.44)" }
          ]),
          borderRadius: [18, 18, 18, 18]
        },
        barWidth: 14
      },

      {
        name: "男",
        type: "bar",
        stack: "总量1",
        data: expenditureDataNum,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: "rgba(130, 152, 255, 1)" },
            { offset: 1, color: "rgba(134, 153, 244, 0.76)" }
          ]),
          borderRadius: [18, 18, 18, 18]
        },
        barWidth: 14
      }
    ]
  };
};
// 【优化4】统一更新逻辑（初始化与数据变化复用）
const updateChart = () => {
  if (!myChart || !chartRef.value) return;
  myChart.setOption(createChartOption(), { notMerge: true });
};
// 【优化5】防抖处理窗口resize
const handleResize = () => {
  resizeTimer && window.clearTimeout(resizeTimer);
  resizeTimer = window.setTimeout(() => myChart?.resize(), 100);
};
// 【优化6】图表初始化函数
const initChart = () => {
  if (!chartRef.value) return;
  // 避免重复初始化
  if (myChart) myChart.dispose();
  myChart = echarts.init(chartRef.value);
  updateChart();
};
// 监听Props变化，实时更新图表
watch(
  () => [
    props.xAxisData,
    props.profitData,
    props.incomeData,
    props.expenditureData,
    props.otherData
  ],
  () => nextTick(updateChart),
  { deep: true }
);
// 生命周期钩子：挂载时初始化+监听resize
onMounted(() => {
  initChart();
  window.addEventListener("resize", handleResize);
});
// 生命周期钩子：卸载时清理资源
onUnmounted(() => {
  window.removeEventListener("resize", handleResize);
  resizeTimer && window.clearTimeout(resizeTimer);
  myChart?.dispose();
  myChart = null;
});
// 暴露外部调用方法
defineExpose({
  initChart,
  getMyChart: () => myChart
});
</script>
<style scoped>
/* 响应式过渡动画，优化视觉体验 */
.chart {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}
</style>