<template>
  <div class="age-radar-chart" ref="chartRef" style="width: 600px; height: 380px"></div>
</template>

<script setup lang="ts">
import { onMounted, ref, onUnmounted, nextTick, computed, watch } from "vue";
import * as echarts from "echarts";

interface AgeData {
  name: string;
  value: number;
}

// 定义 props 接收父组件传递的数据
const props = defineProps<{
  data: AgeData[];
}>();

const chartRef = ref<HTMLElement | null>(null);
let myChart: echarts.ECharts | null = null;

// 使用计算属性确保数据响应式更新
const chartData = computed<AgeData[]>(() => {
  return props.data && props.data.length > 0
    ? props.data
    : [
        { name: "0-18岁", value: 0 },
        { name: "19-30岁", value: 0 },
        { name: "30-40岁", value: 0 },
        { name: "40-50岁", value: 0 },
        { name: "50-60岁", value: 0 }
      ];
});

const initChart = () => {
  if (chartRef.value) {
    if (myChart) {
      myChart.dispose();
    }

    myChart = echarts.init(chartRef.value);
    const dataStyle = {
      normal: {
        color: "rgba(255, 255, 255, 0.08)",
        label: { show: false },
        labelLine: { show: false },
        borderRadius: 0
      },
      emphasis: {
        color: "#393d50"
      }
    };

    const total = chartData.value.reduce((sum, item) => sum + item.value, 0);

    const option = {
      tooltip: {
        show: true,
        trigger: "item",
        formatter: "{a} : {c} ({d}%)",
        backgroundColor: "rgba(0,0,0,0.7)",
        borderColor: "#488AFE",
        borderWidth: 1,
        borderRadius: 8,
        textStyle: {
          color: "#fff"
        }
      },
      color: [
        new echarts.graphic.LinearGradient(0, 0, 1, 1, [
          { offset: 0.06, color: "rgba(255, 119, 119, 0.898)" },
          { offset: 0.91, color: "rgba(255, 119, 119, 0.7412)" }
        ]),
        new echarts.graphic.LinearGradient(0, 0, 1, 1, [
          { offset: 0.06, color: "rgba(226, 108, 255, 1)" },
          { offset: 0.91, color: "rgba(134, 82, 198, 1)" }
        ]),
        new echarts.graphic.LinearGradient(0, 0, 1, 1, [
          { offset: 0.06, color: "rgba(239, 204, 48, 1)" },
          { offset: 0.91, color: "rgba(255, 144, 41, 1)" }
        ]),
        new echarts.graphic.LinearGradient(0, 0, 1, 1, [
          { offset: 0.06, color: "rgba(119, 255, 97, 1)" },
          { offset: 0.91, color: "rgba(0, 193, 135, 1)" }
        ]),
        new echarts.graphic.LinearGradient(0, 0, 1, 1, [
          { offset: 0.06, color: "rgba(66, 157, 237, 1)" },
          { offset: 0.91, color: "rgba(26, 125, 225, 1)" }
        ])
      ],
      legend: {
        orient: "vertical",
        itemWidth: 10,
        itemHeight: 10,
        itemGap: 15,
        left: 0,
        top: 0,
        icon: "circle",
        textStyle: {
          fontSize: 12,
          color: "#fff"
        },
        formatter: (name: string) => {
          const tarValue = chartData.value.find((item) => item.name === name)?.value || 0;
          const percentage = total > 0 ? ((tarValue / total) * 100).toFixed(0) : "0";
          return `${name}  ${percentage}%`;
        }
      },
      series: chartData.value.map((item, index) => {
        const radiusInner = 40 + index * 30;
        const radiusOuter = 60 + index * 30;

        return {
          name: item.name,
          type: "pie",
          radius: [radiusInner, radiusOuter],
          center: ["60%", "50%"],
          label: { show: false },
          labelLine: { show: false },
          emphasis: { label: { show: false, fontSize: "12" } },
          data: [
            {
              value: item.value,
              name: item.name,
              itemStyle: {
                borderRadius: 10
              }
            },
            {
              value: total - item.value,
              tooltip: { show: false },
              itemStyle: dataStyle
            }
          ]
        };
      })
    };

    myChart.setOption(option);
  }
};

// 监听数据变化，重新渲染图表
watch(
  () => props.data,
  () => {
    nextTick(() => {
      initChart();
    });
  },
  { deep: true }
);

const handleResize = () => {
  if (myChart) {
    myChart.resize();
  }
};

onMounted(() => {
  nextTick(() => {
    // initChart();
    window.addEventListener("resize", handleResize);
  });
});

onUnmounted(() => {
  if (myChart) {
    myChart.dispose();
  }
  window.removeEventListener("resize", handleResize);
});
// 暴露方法给父组件使用
defineExpose({
  // 初始化图表
  initChart: () => initChart(),
  myChart: () => myChart
});
</script>

<style scoped>
.age-radar-chart {
  width: 100%;
  height: 100%;
}
</style>