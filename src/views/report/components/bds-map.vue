<template>
  <div class="w-full h-full z-1 relative">
    <div ref="bdsMap" class="c-common-map w-full h-full z-1"></div>
  </div>
</template>

<script setup lang="ts">
import { BdsBaseData } from "@/views/report/type";
import { useBdsTitleHook } from "@/views/report/hooks/useBdsTitleHook";
import { useBdsAreaHook } from "@/views/report/hooks/useBdsAreaHook";
import { useBdsMarkerHook, type BubbleData } from "@/views/report/hooks/useBdsMarkerHook";

// 类型定义增强：使用 TypeScript 字面量类型、可选链等语法
interface AreaDataItem {
  bds_id: string;
  name: string;
  position: {
    lat: number;
    lng: number;
    height?: number;
  };
  icon?: string;
  popupData?: {
    station_name: string;
    station_flow?: number; // 客流数据可选（兼容空值）
    local_visitor_count?: number; // 新增本地客流类型
    non_local_visitor_count?: number; // 新增外地客流类型
  };
}

interface MapCenter {
  lat: number;
  lng: number;
  height?: number;
}

interface MapProps {
  center?: MapCenter;
  mapOptions?: Record<string, unknown>; // 更严谨的类型定义
  showArea?: boolean;
  showName?: boolean;
  showBubble?: boolean;
  bdsBaseData?: BdsBaseData[] | undefined;
  bubbleData?: BubbleData[] | undefined;
  areaData?: AreaDataItem[] | undefined;
}

// 接收 props 并做默认值兜底
const props = withDefaults(defineProps<MapProps>(), {
  center: () => ({ lat: 31.31331, lng: 120.69778 }),
  mapOptions: () => ({}),
  showArea: false,
  showName: false,
  showBubble: false,
  bdsBaseData: () => undefined,
  bubbleData: () => [],
  areaData: () => []
});

// 组件内部状态
const bdsMap: Ref<HTMLElement | null> = ref(null);
const loaded: Ref<boolean> = ref(false);
const TMap = (window as any).TMap; // 需确保 TMap 全局注入
const emit = defineEmits<{
  (event: "load", value: boolean): void;
  (event: "markerClick", id: string): void;
}>();

const mapInstance: Ref<any> = ref(null);

// 使用标记点 hook
const { createMultiMarkers, createMarkerPopups, createBubbleWindows, destroyMarkers } =
  useBdsMarkerHook();

// 地图初始化核心逻辑抽离
const initMap = async (name: string) => {
  if (loaded.value || !bdsMap.value) return;

  try {
    const instance = await new TMap.Map(bdsMap.value, {
      zoom: 14.5,
      viewMode: "3D",
      pitch: 45,
      rotation: -20,
      mapStyleId: "style1",
      zIndex: 10,
      minZoom: 13,
      maxZoom: 18,
      center: new TMap.LatLng(
        props.center?.lat ?? 31.31331,
        props.center?.lng ?? 120.69778,
        props.center?.height
      ),
      ...props.mapOptions,
      baseMap: {
        type: "vector",
        features: ["base", "building3d", "label"],
        buildingRange: [14.5, 23]
      },
      renderOptions: {
        fogOptions: {
          color: "rgba(11, 27, 36, 0.1)"
        }
      }
    });

    mapInstance.value = instance;
    // 移除默认控件（链式调用优化可读性）
    [
      TMap.constants.DEFAULT_CONTROL_ID.ZOOM,
      TMap.constants.DEFAULT_CONTROL_ID.SCALE,
      TMap.constants.DEFAULT_CONTROL_ID.ROTATION
    ].forEach((control) => {
      instance.removeControl(control);
    });

    // 地图加载完成监听
    instance.on("tilesloaded", () => {
      if (!loaded.value) {
        console.log(`${name} 地图加载完成！`);
        loaded.value = true;
        emit("load", true);
        nextTick(() => drawMapElements(instance)); // DOM 更新后渲染
      }
    });
  } catch (error) {
    console.error("地图初始化失败:", error);
    emit("load", true);
  }
};

// 地图元素绘制逻辑抽离
const drawMapElements = (instance: any) => {
  props.showArea && useBdsAreaHook(instance, props.bdsBaseData);
  props.showName && useBdsTitleHook(instance, props.bdsBaseData);
  props.showBubble && createBubbleWindows(instance, props.bubbleData ?? []);
  props.areaData.length > 0 &&
    (createMultiMarkers(instance, props.areaData, (id: string) => emit("markerClick", id)),
    createMarkerPopups(instance, props.areaData));
};

// 组件卸载清理
onUnmounted(() => {
  destroyMarkers();
  mapInstance.value && mapInstance.value.destroy();
  mapInstance.value = null;
});

// 暴露给父组件的方法
defineExpose({
  initMap: (name: string) => {
    nextTick(() => initMap(name)); // 确保 DOM 就绪后初始化
  }
});

// 监听 props 变化自动更新（可选：根据需求开启）
watch(
  [() => props.areaData, () => props.bubbleData],
  () => {
    if (loaded.value && mapInstance.value) {
      drawMapElements(mapInstance.value);
    }
  },
  { deep: true }
);
</script>

<style lang="scss">
.v-marker-popup {
  min-width: 160px;
  background-color: #c3fff6;
  border-radius: 10px;
  position: relative;
  color: #1b3e39;
  padding: 10px;
  user-select: none;
  &::after {
    content: "";
    position: absolute;
    bottom: -9px;
    left: 50%;
    transform: translateX(-50%);
    border-left: 10px solid transparent;
    border-right: 10px solid transparent;
    border-top: 10px solid #c3fff6;
  }

  .popup-text {
    font-size: 14px;
    font-weight: 500;
    margin: 0 0 4px;
  }

  .popup-flow {
    font-size: 13px;
    margin: 0;
    color: #666;
  }
}

.v-bds-bubble-window {
  min-width: 40px;
  min-height: 40px;
  background-color: #c3fff6;
  border-radius: 50%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px 10px;
  user-select: none;
  font-weight: 700;
  color: #020a10;
  font-size: 14px;
  &::after {
    content: "";
    position: absolute;
    bottom: -14px;
    left: 50%;
    transform: translateX(-50%);
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-top: 16px solid #c3fff6;
  }

  &.top-one {
    background-color: #50f4f5;
    &::after {
      border-top-color: #50f4f5;
    }
    font-size: 18px;
  }

  &.top-two {
    background-color: rgba(242, 251, 180, 1);
    &::after {
      border-top-color: rgba(242, 251, 180, 1);
    }
    font-size: 16px;
  }

  &.top-three {
    background-color: #f9b7ff;
    &::after {
      border-top-color: #f9b7ff;
    }
    font-size: 16px;
    color: #5f154e;
  }
}

.c-common-map {
  width: 100%;
  height: 100%;
}
</style>
