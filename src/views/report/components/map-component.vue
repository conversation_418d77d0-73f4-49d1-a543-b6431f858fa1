<template>
  <!-- 地图容器 -->
  <div class="w-full h-full map-container">
    <div ref="mapContainer" class="c-common-map w-full h-full overflow-hidden"></div>
  </div>
</template>

<script lang="ts" setup>
import { useSlideStore } from "@/store/modules/slide";
import { MAP_SLIDE_POSE_TYPE } from "../type";
import { getMarkerIcon } from "./map-icons";

// 扩展Window接口，添加自定义覆盖物元素存储
declare global {
  interface Window {
    customOverlayElements?: Map<
      string,
      {
        element: HTMLElement;
        updatePosition: () => void;
        cleanup: () => void;
      }
    >;
  }
}

/**
 * 地图中心点坐标接口
 */
interface MapCenter {
  lat: number; // 纬度
  lng: number; // 经度
}

/**
 * 标记点接口定义
 */
interface MarkerPoint {
  id: string; // 标记点唯一标识
  position: MapCenter; // 标记点位置坐标
  text?: string; // 标记点显示的文本信息
  averageStayMultiple?: number; // 平均数倍率信息
  style?: {
    icon?: string; // 自定义图标URL
    size?: number; // 图标大小
    color?: string; // 图标颜色
  };
}

/**
 * 围栏区域接口定义
 */
interface FenceArea {
  id: string; // 围栏唯一标识
  points: MapCenter[]; // 围栏边界点坐标数组
  text?: string; // 围栏内显示的文本信息
  style?: {
    borderColor?: string; // 边框颜色
    borderWidth?: number; // 边框宽度
    backgroundColor?: string; // 背景颜色
    opacity?: number; // 透明度
  };
}

/**
 * 自定义DOM覆盖物接口定义
 */
interface CustomDOMOverlay {
  id: string; // 覆盖物唯一标识
  position: MapCenter; // 覆盖物位置坐标
  content: string; // HTML内容
  style?: {
    width?: string; // 宽度
    height?: string; // 高度
    backgroundColor?: string; // 背景颜色
    borderColor?: string; // 边框颜色
    borderWidth?: string; // 边框宽度
    borderRadius?: string; // 圆角半径
    padding?: string; // 内边距
    fontSize?: string; // 字体大小
    color?: string; // 文字颜色
    textAlign?: string; // 文字对齐方式
    boxShadow?: string; // 阴影效果
    zIndex?: number; // 层级
  };
  // 对齐方式
  alignment?:
    | "top-left"
    | "top-center"
    | "top-right"
    | "center-left"
    | "center"
    | "center-right"
    | "bottom-left"
    | "bottom-center"
    | "bottom-right";
}

/**
 * 组件属性接口定义
 */
interface Props {
  center: MapCenter; // 地图中心点坐标
  markers?: MarkerPoint[]; // 标记点数组
  fences?: FenceArea[]; // 围栏数组
  customOverlays?: CustomDOMOverlay[]; // 自定义DOM覆盖物数组
  zoom?: number; // 地图缩放级别
  pitch?: number; // 地图俯仰角
  rotation?: number; // 地图旋转角度
  viewMode?: "2D" | "3D"; // 地图视图模式
}

// 设置默认属性值
const props = withDefaults(defineProps<Props>(), {
  zoom: 14.5, // 默认缩放级别
  pitch: 45, // 默认俯仰角
  rotation: -20, // 默认旋转角度
  viewMode: "3D", // 默认3D视图
  markers: () => [], // 默认空标记点数组
  fences: () => [], // 默认空围栏数组
  customOverlays: () => [] // 默认空自定义覆盖物数组
});

// 引入状态管理
const slideStore = useSlideStore();

// 地图相关响应式变量
const mapContainer = ref(null); // 地图容器DOM引用
const mapInstance: Ref<any> = ref(null); // 地图实例
const loaded = ref(false); // 地图是否加载完成
const isLoading = ref(false); // 地图是否正在加载

// 获取腾讯地图API
const TMap = (window as any).TMap;

/**
 * 初始化地图
 * 创建地图实例并设置基本配置
 */
const initMap = async () => {
  // 如果地图已加载，直接重新绘制元素
  if (loaded.value) {
    drawMapElements();
    return;
  }

  try {
    // 创建地图实例
    mapInstance.value = await new TMap.Map(mapContainer.value, {
      zoom: props.zoom, // 设置缩放级别
      viewMode: props.viewMode, // 设置视图模式
      pitch: props.pitch, // 设置俯仰角
      rotation: props.rotation, // 设置旋转角度
      mapStyleId: "style1", // 地图样式ID
      zIndex: 1, // 层级 - 降低地图层级，避免覆盖卡片组件
      center: new TMap.LatLng(props.center.lat, props.center.lng), // 设置中心点
      baseMap: {
        type: "vector", // 矢量地图
        features: ["base", "building3d", "label"], // 显示要素
        buildingRange: [14.5, 23] // 建筑物显示级别范围
      }
    });

    // 移除默认地图控件
    if (mapInstance.value) {
      mapInstance.value.removeControl(TMap.constants.DEFAULT_CONTROL_ID.ZOOM); // 移除缩放控件
      mapInstance.value.removeControl(TMap.constants.DEFAULT_CONTROL_ID.SCALE); // 移除比例尺控件
      mapInstance.value.removeControl(TMap.constants.DEFAULT_CONTROL_ID.ROTATION); // 移除旋转控件
    }

    // 监听地图瓦片加载完成事件
    mapInstance.value.on("tilesloaded", async () => {
      if (!loaded.value) {
        loaded.value = true; // 标记地图已加载
        slideStore.setLoadMaps("durationStayLocalGuests"); // 通知状态管理

        // 绘制地图元素（标记点、围栏和自定义覆盖物）
        await drawMapElements();
      }
    });
  } catch (error) {
    console.error("地图加载失败:", error);
  } finally {
    isLoading.value = false; // 标记加载完成
  }
};

/**
 * 绘制地图元素
 * 包括标记点、围栏和自定义覆盖物的绘制
 */
const drawMapElements = async () => {
  if (!mapInstance.value) return;

  console.log("绘制地图元素，覆盖物数量:", props.customOverlays.length);

  // 清理现有的自定义覆盖物
  if (window.customOverlayElements) {
    window.customOverlayElements.forEach((overlay: any) => {
      overlay.cleanup();
    });
    window.customOverlayElements.clear();
  }

  // 绘制标记点
  if (props.markers && props.markers.length > 0) {
    await drawMarkers();
  }

  // 绘制围栏
  if (props.fences && props.fences.length > 0) {
    await drawFences();
  }

  // 绘制自定义DOM覆盖物
  if (props.customOverlays && props.customOverlays.length > 0) {
    await drawCustomDOMOverlays();
  }
};

/**
 * 绘制标记点
 * 在地图上添加标记点图层
 */
const drawMarkers = async () => {
  // 使用图标管理文件中的图标

  // 为每个标记点创建独立的样式
  const markerStyles: Record<string, TMap.MarkerStyle> = {};

  props.markers.forEach((marker) => {
    // 根据数值大小确定标记点尺寸
    const markerSize = marker.style?.size || 42;

    // 根据icon属性确定图标路径
    const iconSrc = getMarkerIcon(marker.style?.icon || "default");

    // 计算锚点位置：x为宽度的一半（水平居中），y为高度（底部对齐）
    const anchorX = markerSize / 2;
    const anchorY = markerSize;

    // 为每个标记点创建独立的样式
    markerStyles[marker.id] = new TMap.MarkerStyle({
      width: markerSize, // 标记点宽度，根据数值大小确定
      height: markerSize, // 标记点高度，根据数值大小确定
      anchor: { x: anchorX, y: anchorY }, // 锚点位置，由width和height自动计算
      src: iconSrc // 图标路径，根据icon属性确定
    });
  });

  // 创建标记点图层
  const markerLayer = new TMap.MultiMarker({
    map: mapInstance.value, // 关联地图实例
    styles: markerStyles, // 使用动态创建的样式对象
    // 设置标记点几何信息
    geometries: props.markers.map((marker) => ({
      id: marker.id, // 标记点ID
      styleId: marker.id, // 每个标记点使用自己的样式ID
      position: new TMap.LatLng(marker.position.lat, marker.position.lng) // 位置坐标
    }))
  });

  // 添加标记点点击事件监听
  markerLayer.on("click", (evt: any) => {
    const geometry = evt.geometry;
    if (geometry) {
      console.log("标记点点击:", geometry.id);
      // 这里可以添加自定义的点击处理逻辑
    }
  });

  // 为每个标记点添加文字标签
  if (props.markers && props.markers.length > 0) {
    // 为每个标记点创建独立的标签样式
    const labelStyles: Record<string, TMap.LabelStyle> = {};

    props.markers.forEach((marker) => {
      const offsetY = Math.ceil((marker.style?.size || 50) / 2 + marker.averageStayMultiple * 2);
      // 根据数据属性动态生成标签样式
      const labelStyle = new TMap.LabelStyle({
        color: "#000000", // 文字颜色
        size: Math.floor(Math.max(16, Math.min(16, (marker.style?.size || 40) / 2))), // 文字大小根据标记点尺寸动态调整
        offset: {
          x: 0,
          y: -offsetY // 偏移量根据标记点尺寸动态调整
        },
        padding: {
          x: Math.ceil(Math.max(4, Math.min(8, (marker.style?.size || 40) / 8))), // 内边距根据标记点尺寸动态调整
          y: Math.ceil(Math.max(2, Math.min(4, (marker.style?.size || 40) / 16)))
        }
      });

      labelStyles[marker.id] = labelStyle;
    });

    const labelLayer = new TMap.MultiLabel({
      map: mapInstance.value, // 关联地图实例
      styles: labelStyles, // 使用动态创建的样式对象
      // 设置文字标签几何信息
      geometries: props.markers.map((marker) => ({
        id: `label-${marker.id}`, // 文字标签ID
        styleId: marker.id, // 每个标签使用自己的样式ID
        position: new TMap.LatLng(marker.position.lat, marker.position.lng), // 位置坐标
        content: marker.text || "" // 文字内容
      }))
    });
  }
};

/**
 * 绘制围栏
 * 在地图上添加多边形围栏图层
 */
const drawFences = async () => {
  props.fences.forEach((fence) => {
    // 创建多边形图层
    const polygon = new TMap.MultiPolygon({
      map: mapInstance.value, // 关联地图实例
      styles: {
        fence: new TMap.PolygonStyle({
          color: fence.style?.backgroundColor || "#1890ff", // 背景颜色
          borderColor: fence.style?.borderColor || "#ffffff", // 边框颜色
          borderWidth: fence.style?.borderWidth || 2, // 边框宽度
          opacity: fence.style?.opacity || 0.3 // 透明度
        })
      },
      // 设置多边形几何信息
      geometries: [
        {
          id: fence.id, // 围栏ID
          styleId: "fence", // 样式ID
          paths: fence.points.map((point) => new TMap.LatLng(point.lat, point.lng)) // 边界点坐标
        }
      ]
    });

    // 如果围栏有文本信息，添加文本标注
    if (fence.text) {
      const centerPoint = calculatePolygonCenter(fence.points); // 计算围栏中心点

      // 创建文本标注图层
      const textLayer = new TMap.MultiLabel({
        map: mapInstance.value, // 关联地图实例
        styles: {
          "fence-text": new TMap.LabelStyle({
            color: "#ffffff", // 文本颜色
            size: 14, // 文本大小
            offset: { x: 0, y: 0 }, // 偏移量
            backgroundColor: "rgba(0,0,0,0.5)", // 背景颜色
            borderRadius: 4, // 圆角半径
            padding: { x: 8, y: 4 } // 内边距
          })
        },
        // 设置文本几何信息
        geometries: [
          {
            id: `text-${fence.id}`, // 文本ID
            styleId: "fence-text", // 样式ID
            position: new TMap.LatLng(centerPoint.lat, centerPoint.lng), // 位置坐标
            content: fence.text // 文本内容
          }
        ]
      });
    }
  });
};

/**
 * 计算多边形中心点
 * 用于在围栏中心显示文本标注
 * @param points 多边形顶点坐标数组
 * @returns 中心点坐标
 */
const calculatePolygonCenter = (points: MapCenter[]): MapCenter => {
  if (points.length === 0) return { lat: 0, lng: 0 };

  // 计算所有顶点的平均值作为中心点
  const sumLat = points.reduce((sum, point) => sum + point.lat, 0);
  const sumLng = points.reduce((sum, point) => sum + point.lng, 0);

  return {
    lat: sumLat / points.length,
    lng: sumLng / points.length
  };
};

/**
 * 绘制自定义覆盖物
 * 在地图上添加各种类型的自定义覆盖物
 */
const drawCustomDOMOverlays = async () => {
  console.log("开始绘制自定义覆盖物，数量:", props.customOverlays.length);
  props.customOverlays.forEach((overlay) => {
    console.log("创建覆盖物:", overlay.id, overlay.position);
    createDOMOverlay(overlay);
  });
};

/**
 * 创建自定义DOM覆盖物
 * 参照腾讯地图官方文档实现
 */
const createDOMOverlay = (overlay: CustomDOMOverlay) => {
  try {
    // 创建DOM元素
    const domElement = document.createElement("div");

    // 设置DOM内容
    domElement.innerHTML = overlay.content;

    // 设置DOM样式
    const defaultStyle = {
      position: "absolute",
      top: "0",
      left: "0",
      width: overlay.style?.width || "auto",
      height: overlay.style?.height || "auto",
      backgroundColor: overlay.style?.backgroundColor || "rgba(0,0,0,0)",
      border:
        overlay.style?.borderWidth && overlay.style?.borderColor
          ? `${overlay.style.borderWidth} solid ${overlay.style.borderColor}`
          : "none",
      borderRadius: overlay.style?.borderRadius || "50%",
      padding: overlay.style?.padding || "3px 6px",
      fontSize: overlay.style?.fontSize || "12px",
      color: overlay.style?.color || "#ffffff",
      textAlign: overlay.style?.textAlign || "center",
      zIndex: overlay.style?.zIndex || 0,
      pointerEvents: "auto",
      userSelect: "none"
    };

    // 应用样式
    Object.assign(domElement.style, defaultStyle);

    // 将DOM元素添加到地图容器
    mapContainer.value.appendChild(domElement);

    console.log("覆盖物DOM元素已创建:", overlay.id);

    // 更新DOM位置函数
    const updateDOMPosition = () => {
      if (!mapInstance.value || !domElement) return;

      try {
        // 经纬度坐标转容器像素坐标
        const pixel = mapInstance.value.projectToContainer(
          new TMap.LatLng(overlay.position.lat, overlay.position.lng)
        );

        if (!pixel) return;

        let left = pixel.getX();
        let top = pixel.getY();

        // 计算偏移量
        const domRect = domElement.getBoundingClientRect();
        // 修正offsetX，考虑页面缩放（缩放后getBoundingClientRect宽度会变化）
        // 获取当前缩放比例
        const scale = domElement.offsetWidth > 0 ? domRect.width / domElement.offsetWidth : 1;
        // 计算修正后除数的值
        const divide = 2 * scale;
        const offsetX = left - domRect.width / divide;

        // 应用位置
        domElement.style.transform = `translate3d(${Math.floor(offsetX)}px, ${Math.floor(
          top
        )}px, 0px)`;
      } catch (error) {
        console.error("更新覆盖物位置失败:", error);
      }
    };

    // 初始更新位置
    updateDOMPosition();

    // 监听地图事件，更新DOM位置
    mapInstance.value.on("zoom", updateDOMPosition);
    mapInstance.value.on("pan", updateDOMPosition);
    mapInstance.value.on("pitch", updateDOMPosition);
    mapInstance.value.on("rotation", updateDOMPosition);

    // 存储DOM元素引用，用于后续清理
    if (!window.customOverlayElements) {
      window.customOverlayElements = new Map();
    }
    window.customOverlayElements.set(overlay.id, {
      element: domElement,
      updatePosition: updateDOMPosition,
      cleanup: () => {
        try {
          // 移除事件监听
          mapInstance.value.off("zoom", updateDOMPosition);
          mapInstance.value.off("pan", updateDOMPosition);
          mapInstance.value.off("pitch", updateDOMPosition);
          mapInstance.value.off("rotation", updateDOMPosition);
          // 移除DOM元素
          if (domElement.parentNode) {
            domElement.parentNode.removeChild(domElement);
          }
          console.log("覆盖物已清理:", overlay.id);
        } catch (error) {
          console.error("清理覆盖物失败:", error);
        }
      }
    });
  } catch (error) {
    console.error("创建覆盖物失败:", overlay.id, error);
  }
};

/**
 * 监听属性变化
 * 当标记点、围栏或自定义覆盖物数据发生变化时，重新绘制地图元素
 */
watch(
  [() => props.markers, () => props.fences, () => props.customOverlays],
  (newVal) => {
    console.log("地图元素数据变化，新值:", newVal);
    if (loaded.value && mapInstance.value) {
      // 清除现有元素并重新绘制
      drawMapElements();
    }
  },
  { deep: true }
); // 深度监听数组内容变化

/**
 * 组件挂载时初始化地图
 */
onMounted(() => {});

/**
 * 组件卸载时清理自定义覆盖物
 */
onUnmounted(() => {
  if (window.customOverlayElements) {
    window.customOverlayElements.forEach((overlay: any) => {
      overlay.cleanup();
    });
    window.customOverlayElements.clear();
  }
});

/**
 * 暴露组件方法
 * 实现 MAP_SLIDE_POSE_TYPE 接口
 */
interface MAP_FUNCTION_EXPORT extends MAP_SLIDE_POSE_TYPE {
  initMap: () => void;
  getMapInstance: () => any;
  destroy: () => void;
}
defineExpose<MAP_FUNCTION_EXPORT>({
  name: "durationStayLocalGuests", // 组件名称
  preloadMap: () => {
    if (loaded.value) {
      // 如果地图已加载，直接通知状态管理
      slideStore.setLoadMaps("durationStayLocalGuests");
    } else {
      // 如果地图未加载，延迟初始化
      setTimeout(() => {
        initMap();
      }, 100);
    }
  },
  initMap: () => {
    initMap();
  },
  getMapInstance: () => {
    return mapInstance.value;
  },
  destroy: () => {
    // 清理覆盖物
    if (window.customOverlayElements) {
      window.customOverlayElements.forEach((overlay) => overlay.cleanup());
      window.customOverlayElements.clear();
    }
    // 销毁地图实例
    if (mapInstance.value && mapInstance.value.destroy) {
      mapInstance.value.destroy();
    }
    // 重置状态
    loaded.value = false;
    mapInstance.value = null;
  }
});
</script>

<style scoped>
.map-container {
  position: relative;
  z-index: 1;
}
</style>
