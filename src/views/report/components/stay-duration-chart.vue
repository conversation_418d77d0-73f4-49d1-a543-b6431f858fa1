<template>
  <div class="chart-container" style="pointer-events: auto">
    <div ref="chartRef" class="chart-canvas"></div>
  </div>
</template>

<script setup lang="ts">
import * as echarts from "echarts";

interface ChartDataItem {
  name: string;
  value: number;
  count: number;
}

interface Props {
  chartData: ChartDataItem[];
  districtName: string;
}

const props = defineProps<Props>();

const chartRef = ref<HTMLElement>();
let chartInstance: echarts.ECharts | null = null;

// 严格还原参考图配色（按顺序对应各分段）
const colors = [
  "#FFC800", // 17-22点
  "#57FFD5", // 14-17点
  "#35C6FF", // 11-14点
  "#FF759E", // 9-11点
  "#368EF9" // 22-次日9点
];

const initChart = () => {
  if (!chartRef.value) return;
  chartInstance = echarts.init(chartRef.value, { renderer: "svg" });
  updateChart();
};

const updateChart = () => {
  if (!chartInstance || !props.chartData.length) return;

  // 预生成 rich 配置
  const richConfig = {};
  props.chartData.forEach((_, index) => {
    richConfig[`valueStyle${index}`] = {
      fontSize: 12,
      fontWeight: "bold",
      color: colors[index % colors.length]
    };
  });

  const option: echarts.EChartsOption = {
    backgroundColor: "transparent",
    tooltip: {
      trigger: "item",
      formatter: (params: any) => `
        <div style="background: #0E2A40; padding: 8px 12px; border-radius: 6px; border: 1px solid #35C6FF;">
          <div style="font-size: 14px; color: #fff; margin-bottom: 4px;">消费人数 ${params.data.count}万</div>
          <div style="font-size: 12px; color: #fff; opacity: 0.8;">消费人数占比 ${params.data.value}%</div>
        </div>
      `,
      backgroundColor: "rgba(0,0,0,0)",
      borderColor: "transparent",
      padding: 0,
      textStyle: { color: "#fff" },
      extraCssText: "pointer-events: auto;"
    },
    series: [
      {
        name: "停留时长",
        type: "pie",
        radius: ["40%", "60%"],
        center: ["50%", "50%"],
        clockwise: true,
        label: {
          show: true,
          position: "outside",
          formatter: (params: any) => {
            // 为每个数据项使用对应的 rich 样式
            return [
              `{nameStyle|${params.name}}`,
              `{valueStyle${params.dataIndex}|${params.value}%}`
            ].join("\n");
          },
          rich: {
            nameStyle: {
              fontSize: 14,
              color: "#fff",
              lineHeight: 18,
              align: "left"
            },
            // 动态生成的 rich 配置
            ...richConfig
          }
        },
        labelLine: {
          show: true,
          length: 10,
          length2: 30,
          lineStyle: {
            width: 2,
            opacity: 0.8
          }
        },
        data: props.chartData.map((item, index) => ({
          name: item.name,
          value: item.value,
          count: item.count,
          itemStyle: {
            color: colors[index % colors.length]
          }
        }))
      }
    ]
  };

  chartInstance.setOption(option, true);
};

watch(
  () => props.chartData,
  () => nextTick(updateChart),
  { deep: true }
);

const handleResize = () => chartInstance?.resize();

onMounted(() => {
  initChart();
  window.addEventListener("resize", handleResize);
});

onUnmounted(() => {
  chartInstance?.dispose();
  window.removeEventListener("resize", handleResize);
});
</script>

<style scoped lang="scss">
.chart-container {
  width: 100%;
  height: 300px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}
.chart-canvas {
  width: 100%;
  height: 100%;
}
</style>