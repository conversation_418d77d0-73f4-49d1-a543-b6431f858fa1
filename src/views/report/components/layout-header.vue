<template>
  <div class="v-layout-header flex items-center fs-18">
    <el-image :src="IconLogo" alt="bwton-logo" class="w-100 h-45 ml-24" />
    <p class="fs-26 c-fff ml-10 fw-600">消费数据监测分析报告</p>

    <div class="ml-10 w-100">
      <div>
        <el-image :src="IconBlank" alt="IconBlank" class="w-14 h-9 mr-8" />
        <span class="fs-12">商业区域</span>
      </div>
      <div>
        <el-image :src="IconLocation" alt="IconLocation" class="w-12 h-14 mr-8" />
        <span class="fs-12">商业数据展示</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import IconLogo from "@/assets/icons/icon-bwton-logo.png";
import IconBlank from "@/assets/icons/icon-header-blank.png";
import IconLocation from "@/assets/icons/icon-header-location.png";
</script>

<style lang="scss" scoped>
.v-layout-header {
  position: absolute;
  top: 0;
  left: 0;
  width: 40vw;
  z-index: 1200;
  height: 60px;
  color: #fff;
  background-color: transparent;
}
</style>
