<template>
  <div class="reveal" ref="revealContainer" :style="{ visibility: ready ? 'visible' : 'hidden' }">
    <div class="slides">
      <section
        v-for="(slide, index) in slides"
        :key="slide.name"
        :style="{ width: screenRatio.width + 'px', height: screenRatio.height + 'px' }"
      >
        <component :is="slide.component" ref="slideRef" :data-index="index" />
      </section>
    </div>
  </div>
</template>

<script setup lang="ts">
import { SLIDE_COMPONENTS } from "./config";
import { ElLoading } from "element-plus";
import { useSlideStore } from "@/store/modules/slide";

import Reveal from "reveal.js";
import "reveal.js/dist/reveal.css";

const loadingInstance = ElLoading.service({
  background: "#0b1b24",
  customClass: "c-b1fff2",
  text: "研报数据生成中...",
  fullscreen: true
});

const ready = ref(false);

const slides = ref(SLIDE_COMPONENTS);

const revealInstance = ref<Reveal.Api | null>(null);
const revealContainer = ref<HTMLElement | null>(null);
const slidesRefs = useTemplateRef("slideRef");
const slideStore = useSlideStore();
const mapSlideLen = slides.value.filter((slide) => slide.hasMap).length;

slideStore.setTotalSlideCount(slides.value.length);

watch(
  () => slideStore.currentSlide,
  (newValue) => {
    if (slideStore.loadedMaps.length !== mapSlideLen) {
      preloadMaps(newValue);
    }
  }
);

watch(
  () => slideStore.loadedMaps,
  (newValue) => {
    if (newValue.length == 1) {
      console.log("有地图加载完成了");
      ready.value = true;
      loadingInstance && loadingInstance.close();
    }
  }
);
// 预加载地图
const preloadMaps = (index: number) => {
  // 提取近三个slide的信息
  const nearbySlides = slides.value.slice(
    Math.max(index, 0),
    Math.min(index + 2, slides.value.length)
  );
  nearbySlides.forEach((slide) => {
    if (slide.hasMap) {
      const slideComponent = slidesRefs.value?.find((it: any) => it.name === slide.name);
      slideComponent && (slideComponent as any).preloadMap();
    }
  });
};

// 初始化Reveal.js
const initReveal = () => {
  if (!revealContainer.value) return;
  revealInstance.value = new Reveal(revealContainer.value, {
    ...screenRatio.value,
    margin: 0,
    overview: false,
    center: false,
    hash: false, // 是否允许URL hash导航
    transition: "slide", // 过渡效果
    controls: false, // 是否显示控制按钮
    progress: true, // 是否显示进度条
    slideNumber: false, // 是否显示幻灯片编号
    keyboard: true, // 是否支持键盘导航
    viewDistance: 2
  });

  revealInstance.value.initialize({});
  // 监听幻灯片切换事件
  revealInstance.value.on("slidechanged", (event: any) => {
    slideStore.actionJumpToSlide(event.indexh);
  });

  preloadMaps(0);
};

// 组件挂载时初始化
onMounted(() => {
  nextTick(() => {
    initReveal();
  });
});

onUnmounted(() => {
  // 清理Reveal实例
  if (revealInstance.value) {
    revealInstance.value.destroy();
  }
});

watch(
  () => slideStore.currentSlide,
  (newValue) => {
    if (revealInstance.value) {
      revealInstance.value.slide(newValue);
    }
  }
);

// 获取屏幕的宽度和高度比例
const screenRatio = computed(() => {
  const { width, height } = window.screen;
  return {
    width: 1600,
    height: width / height < 16 / 9 ? 1000 : 900
  };
});
</script>

<style lang="scss">
.reveal {
  background-color: #0b1b24;
  .slides {
    text-align: left;
    > section {
      padding: 0;
      box-sizing: border-box;
    }
  }
}
</style>
